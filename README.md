# Aggie - AI-Powered Invoice Management Platform

A secure, multi-tenant SaaS platform for automated invoice processing using AI and RAG (Retrieval-Augmented Generation).

## Features

- **Multi-Tenant Architecture**: Secure data isolation with Row-Level Security (RLS)
- **AI-Powered Processing**: Automated invoice interpretation and accounting suggestions
- **Advanced Security**: JWT authentication with TOTP 2FA support
- **Human-in-the-Loop**: Manual review for uncertain AI decisions
- **OCR Support**: Extract text from PDF, PNG, and JPG invoice files
- **Modular LLM**: Support for OpenAI and Azure OpenAI
- **Containerized**: Full Docker deployment with development and production configurations

## Tech Stack

- **Backend**: FastAPI, SQLAlchemy, PostgreSQL with pg_vector
- **Frontend**: React with TypeScript
- **AI/ML**: OpenAI GPT-4o-mini, RAG with vector embeddings
- **Queue**: Celery with Redis
- **Containerization**: Docker & Docker Compose

## Quick Start

### Development Environment (with Hot Reload)

1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd aggie
   cp .env.example .env
   ```

2. **Configure environment**:
   Edit `.env` file with your OpenAI API key and other settings.

3. **Start development services with hot reload**:
   ```bash
   # Recommended: Development mode with hot reload
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

   # Or use Make commands (if available)
   make dev-up
   ```

4. **Access the application**:
   - **Admin Console**: http://localhost:3001
   - **User App**: http://localhost:3002
   - **Backend API**: http://localhost:8000
   - **API Documentation**: http://localhost:8000/docs

5. **Development features**:
   - ✅ **Hot reload** for both frontend and backend
   - ✅ **Live debugging** with source maps
   - ✅ **Automatic restart** on code changes
   - ✅ **Enhanced logging** for development

   See [Development Guide](./docs/DEVELOPMENT.md) for detailed workflow.

### Production Environment

1. **Setup environment**:
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

2. **Deploy**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

## Project Structure

```
aggie/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── models/         # SQLAlchemy models
│   │   ├── routers/        # API endpoints
│   │   ├── services/       # Business logic
│   │   ├── tasks/          # Celery tasks
│   │   └── utils/          # Utilities
│   ├── alembic/            # Database migrations
│   └── tests/              # Backend tests
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utilities
│   └── public/
└── docker-compose.yml      # Development setup
```

## Security Features

- **Row-Level Security (RLS)**: Database-level tenant isolation
- **JWT Authentication**: Secure token-based authentication
- **TOTP 2FA**: Time-based one-time password support
- **Encrypted Storage**: Sensitive data encrypted at rest
- **GDPR Compliance**: Built-in data protection features

## API Documentation

Once running, visit http://localhost:8000/docs for interactive API documentation.

## Testing

Run backend tests:
```bash
cd backend
pytest
```

Run frontend tests:
```bash
cd frontend
npm test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Your License Here]
