"""Enable Row Level Security

Revision ID: 003
Revises: 002
Create Date: 2024-01-01 10:02:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Enable RLS on tenant-aware tables
    op.execute("ALTER TABLE invoices ENABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE accounting_entries ENABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE invoice_vectors ENABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE action_items ENABLE ROW LEVEL SECURITY")
    
    # Create RLS policies for invoices
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON invoices
        FOR ALL
        TO PUBLIC
        USING (tenant_id::text = current_setting('app.current_tenant_id', true))
        WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
    """)
    
    # Create RLS policies for accounting_entries
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON accounting_entries
        FOR ALL
        TO PUBLIC
        USING (tenant_id::text = current_setting('app.current_tenant_id', true))
        WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
    """)
    
    # Create RLS policies for invoice_vectors
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON invoice_vectors
        FOR ALL
        TO PUBLIC
        USING (tenant_id::text = current_setting('app.current_tenant_id', true))
        WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
    """)
    
    # Create RLS policies for action_items
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON action_items
        FOR ALL
        TO PUBLIC
        USING (tenant_id::text = current_setting('app.current_tenant_id', true))
        WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
    """)
    
    # Create indexes for better performance with RLS
    op.create_index('idx_invoices_tenant_status', 'invoices', ['tenant_id', 'status'])
    op.create_index('idx_accounting_entries_tenant_invoice', 'accounting_entries', ['tenant_id', 'invoice_id'])
    op.create_index('idx_action_items_tenant_user', 'action_items', ['tenant_id', 'user_id'])
    op.create_index('idx_action_items_tenant_completed', 'action_items', ['tenant_id', 'is_completed'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_action_items_tenant_completed', table_name='action_items')
    op.drop_index('idx_action_items_tenant_user', table_name='action_items')
    op.drop_index('idx_accounting_entries_tenant_invoice', table_name='accounting_entries')
    op.drop_index('idx_invoices_tenant_status', table_name='invoices')
    
    # Drop RLS policies
    op.execute("DROP POLICY IF EXISTS tenant_isolation_policy ON action_items")
    op.execute("DROP POLICY IF EXISTS tenant_isolation_policy ON invoice_vectors")
    op.execute("DROP POLICY IF EXISTS tenant_isolation_policy ON accounting_entries")
    op.execute("DROP POLICY IF EXISTS tenant_isolation_policy ON invoices")
    
    # Disable RLS
    op.execute("ALTER TABLE action_items DISABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE invoice_vectors DISABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE accounting_entries DISABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE invoices DISABLE ROW LEVEL SECURITY")
