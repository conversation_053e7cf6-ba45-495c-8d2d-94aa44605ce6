


[1m[33mwarn[39m[22m - The `content` option in your Tailwind CSS configuration is missing or empty.
[1m[33mwarn[39m[22m - Configure your content sources or your generated CSS will be missing styles.
[1m[33mwarn[39m[22m - https://tailwindcss.com/docs/content-configuration
Source path: D:\Visual Code Repo\aggie\apps\admin-frontend\src\index.css
Setting up new context...
Finding changed files: 0.256ms
Reading changed files: 0.006ms
Sorting candidates: 0.003ms
Generate rules: 1.725ms
Build stylesheet: 0.485ms
Potential classes:  [33m1[39m
Active contexts:  [33m1[39m
JIT TOTAL: 81.438ms


Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 384 KiB = [1m[32mruntime.3bdc9e116e457401.js[39m[22m 1.81 KiB [1m[32mmain.9a7e9e1fdb10db38.css[39m[22m 4.82 KiB [1m[32mmain.b79ef04e8bbd519a.js[39m[22m 378 KiB
Entrypoint [1mpolyfills[39m[22m 1.93 KiB = [1m[32mruntime.3bdc9e116e457401.js[39m[22m 1.81 KiB [1m[32mpolyfills.5110ca83c0782d9b.js[39m[22m 123 bytes
Entrypoint [1mstyles[39m[22m 6.77 KiB = [1m[32mruntime.3bdc9e116e457401.js[39m[22m 1.81 KiB [1m[32mstyles.9a7e9e1fdb10db38.css[39m[22m 4.82 KiB [1m[32mstyles.2ff0f3b310d95f63.js[39m[22m 136 bytes
chunk (runtime: runtime) [1m[32mruntime.3bdc9e116e457401.js[39m[22m (runtime) 4.64 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mpolyfills.5110ca83c0782d9b.js[39m[22m (polyfills) 3.55 KiB [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mmain.9a7e9e1fdb10db38.css[39m[22m, [1m[32mmain.b79ef04e8bbd519a.js[39m[22m (main) 942 KiB (javascript) 10.3 KiB (css/mini-extract) [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mstyles.9a7e9e1fdb10db38.css[39m[22m, [1m[32mstyles.2ff0f3b310d95f63.js[39m[22m (styles) 50 bytes (javascript) 10.3 KiB (css/mini-extract) [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (76b86b4b64c3424e)
