"""Create new invoice and session system

Revision ID: 011
Revises: 010
Create Date: 2025-01-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '011'
down_revision = '010'
branch_labels = None
depends_on = None


def upgrade():
    # Drop old AI agent tables if they exist
    op.execute("DROP TABLE IF EXISTS thought_chains CASCADE")
    op.execute("DROP TABLE IF EXISTS tool_results CASCADE")
    op.execute("DROP TABLE IF EXISTS execution_steps CASCADE")
    op.execute("DROP TABLE IF EXISTS agent_sessions CASCADE")
    op.execute("DROP TABLE IF EXISTS execution_plans CASCADE")
    
    # Drop old enums if they exist
    op.execute("DROP TYPE IF EXISTS sessionstatus CASCADE")
    op.execute("DROP TYPE IF EXISTS stepstatus CASCADE")
    
    # Update invoices table structure
    # Add new columns
    op.add_column('invoices', sa.Column('import_typ', sa.String(length=50), nullable=True))
    op.add_column('invoices', sa.Column('metadata_ERP', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('invoices', sa.Column('file_data', sa.Text(), nullable=True))
    
    # Make some existing columns nullable since they'll be populated later
    op.alter_column('invoices', 'supplier_name', nullable=True)
    op.alter_column('invoices', 'original_filename', nullable=True)
    op.alter_column('invoices', 'file_path', nullable=True, new_column_name='file_path_deprecated')
    op.alter_column('invoices', 'file_type', nullable=True)
    
    # Set default values for existing records
    op.execute("UPDATE invoices SET import_typ = 'manuell' WHERE import_typ IS NULL")
    
    # Make import_typ and file_data non-nullable after setting defaults
    op.alter_column('invoices', 'import_typ', nullable=False)
    # Note: file_data will be populated by a separate migration script
    
    # Create sessions table
    op.create_table('sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('invoice_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('current_step', sa.String(length=50), nullable=True),
        sa.Column('extracted_data', sa.Text(), nullable=True),
        sa.Column('extracted_reasoning', sa.Text(), nullable=True),
        sa.Column('context_data', sa.Text(), nullable=True),
        sa.Column('context_reasoning', sa.Text(), nullable=True),
        sa.Column('account_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('account_reasoning', sa.Text(), nullable=True),
        sa.Column('booking_result', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('booking_reasoning', sa.Text(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('failed_step', sa.String(length=50), nullable=True),
        sa.ForeignKeyConstraint(['invoice_id'], ['invoices.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('invoice_id')
    )
    
    # Create session_logs table
    op.create_table('session_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('step_name', sa.String(length=50), nullable=False),
        sa.Column('prompt_sent', sa.Text(), nullable=False),
        sa.Column('llm_response', sa.Text(), nullable=False),
        sa.Column('reasoning', sa.Text(), nullable=True),
        sa.Column('execution_time_ms', sa.Float(), nullable=True),
        sa.Column('success', sa.Boolean(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['session_id'], ['sessions.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('ix_sessions_tenant_id', 'sessions', ['tenant_id'])
    op.create_index('ix_sessions_status', 'sessions', ['status'])
    op.create_index('ix_sessions_invoice_id', 'sessions', ['invoice_id'])
    
    op.create_index('ix_session_logs_tenant_id', 'session_logs', ['tenant_id'])
    op.create_index('ix_session_logs_session_id', 'session_logs', ['session_id'])
    op.create_index('ix_session_logs_step_name', 'session_logs', ['step_name'])
    
    op.create_index('ix_invoices_import_typ', 'invoices', ['import_typ'])
    
    # Set default status for sessions
    op.execute("ALTER TABLE sessions ALTER COLUMN status SET DEFAULT 'pending'")
    op.execute("ALTER TABLE session_logs ALTER COLUMN success SET DEFAULT true")


def downgrade():
    # Drop new tables
    op.drop_table('session_logs')
    op.drop_table('sessions')
    
    # Revert invoice table changes
    op.drop_column('invoices', 'import_typ')
    op.drop_column('invoices', 'metadata_ERP')
    op.drop_column('invoices', 'file_data')
    
    # Revert column changes
    op.alter_column('invoices', 'supplier_name', nullable=False)
    op.alter_column('invoices', 'original_filename', nullable=False)
    op.alter_column('invoices', 'file_path_deprecated', nullable=False, new_column_name='file_path')
    op.alter_column('invoices', 'file_type', nullable=False)
