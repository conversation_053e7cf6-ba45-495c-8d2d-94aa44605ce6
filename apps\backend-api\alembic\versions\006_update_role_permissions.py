"""Update role permissions to include action_items

Revision ID: 006
Revises: 005
Create Date: 2024-01-01 10:06:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '006'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Update admin role permissions
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read", "invoices:write", "invoices:delete",
            "users:read", "users:write", "users:delete",
            "accounting:read", "accounting:write", "accounting:validate",
            "settings:read", "settings:write",
            "reports:read", "reports:write",
            "action_items:read", "action_items:write", "action_items:assign"
        ]'::json
        WHERE name = 'admin'
    """)
    
    # Update manager role permissions
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read", "invoices:write",
            "users:read", "users:write",
            "accounting:read", "accounting:validate",
            "reports:read", "reports:write",
            "action_items:read", "action_items:write", "action_items:assign"
        ]'::json
        WHERE name = 'manager'
    """)
    
    # Update accountant role permissions
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read", "invoices:write",
            "accounting:read", "accounting:write", "accounting:validate",
            "reports:read",
            "action_items:read", "action_items:write"
        ]'::json
        WHERE name = 'accountant'
    """)
    
    # Update viewer role permissions
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read",
            "accounting:read",
            "reports:read",
            "action_items:read"
        ]'::json
        WHERE name = 'viewer'
    """)


def downgrade() -> None:
    # Revert to original permissions (without action_items)
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read", "invoices:write", "invoices:delete",
            "users:read", "users:write", "users:delete",
            "settings:read", "settings:write",
            "reports:read", "reports:write"
        ]'::json
        WHERE name = 'admin'
    """)
    
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read", "invoices:write",
            "users:read", "users:write",
            "reports:read", "reports:write"
        ]'::json
        WHERE name = 'manager'
    """)
    
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read", "invoices:write",
            "accounting:read", "accounting:write",
            "reports:read"
        ]'::json
        WHERE name = 'accountant'
    """)
    
    op.execute("""
        UPDATE roles 
        SET permissions = '[
            "invoices:read",
            "reports:read"
        ]'::json
        WHERE name = 'viewer'
    """)
