"""Fix encryption configuration for integration models

Revision ID: 008
Revises: 007
Create Date: 2024-01-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '008'
down_revision = '007'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # This migration handles the encryption configuration fix
    # The actual encryption changes are in the model definitions
    # We just need to ensure the tables can handle the new encryption format
    
    # Add a comment to track the encryption fix
    op.execute("""
        COMMENT ON TABLE invoice_integrations IS 
        'Invoice integrations with fixed encryption configuration (v008)'
    """)
    
    op.execute("""
        COMMENT ON TABLE oauth2_tokens IS 
        'OAuth2 tokens with fixed encryption configuration (v008)'
    """)
    
    # No structural changes needed - the encryption fix is in the application layer


def downgrade() -> None:
    # Remove comments
    op.execute("COMMENT ON TABLE invoice_integrations IS NULL")
    op.execute("COMMENT ON TABLE oauth2_tokens IS NULL")
