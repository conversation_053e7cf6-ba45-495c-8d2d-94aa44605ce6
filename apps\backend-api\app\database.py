from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from app.config import settings
import logging

logger = logging.getLogger(__name__)

# Create engine with proper configuration
if "sqlite" in settings.database_url:
    # SQLite configuration
    engine = create_engine(
        settings.database_url,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False, "options": "-c timezone=UTC"},
        echo=settings.debug,
    )
else:
    # PostgreSQL configuration
    engine = create_engine(
        settings.database_url,
        connect_args={
            "options": "-c timezone=UTC",
            "connect_timeout": 30,  # 30 second connection timeout
            "application_name": "aggie_backend_api"
        },
        echo=settings.debug,
        pool_timeout=30,  # 30 second pool timeout
        pool_recycle=3600,  # Recycle connections after 1 hour
        pool_pre_ping=True,  # Verify connections before use
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def set_tenant_context(db, tenant_id: str):
    """Set the tenant context for Row-Level Security"""
    try:
        logger.info(f"Setting tenant context to: {tenant_id}")
        db.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, true)"),
                  {"tenant_id": tenant_id})
        db.commit()
        logger.info(f"Successfully set tenant context to: {tenant_id}")
    except Exception as e:
        logger.error(f"Failed to set tenant context: {e}")
        db.rollback()
        raise


def clear_tenant_context(db):
    """Clear the tenant context"""
    try:
        db.execute(text("SELECT set_config('app.current_tenant_id', '', true)"))
        db.commit()
        logger.debug("Cleared tenant context")
    except Exception as e:
        logger.error(f"Failed to clear tenant context: {e}")
        db.rollback()
        raise


def recover_database_session(db):
    """
    Recover a database session that may be in a bad state due to rollback

    This function attempts to clear any pending rollback state and ensure
    the session is ready for new operations.
    """
    try:
        # Check if the session is in a rollback state
        if db.is_active and db.in_transaction():
            logger.warning("Database session is in transaction state, attempting rollback")
            db.rollback()

        # Test the connection with a simple query
        db.execute(text("SELECT 1"))
        logger.debug("Database session recovered successfully")

    except Exception as e:
        logger.error(f"Failed to recover database session: {e}")
        # If recovery fails, close and recreate the session
        try:
            db.close()
        except Exception:
            pass
        raise


def safe_commit(db, operation_name: str = "operation"):
    """
    Safely commit a database transaction with proper error handling

    Args:
        db: Database session
        operation_name: Name of the operation for logging

    Returns:
        bool: True if commit succeeded, False otherwise
    """
    try:
        db.commit()
        logger.debug(f"Successfully committed {operation_name}")
        return True
    except Exception as e:
        logger.error(f"Failed to commit {operation_name}: {e}")
        try:
            db.rollback()
            logger.debug(f"Rolled back {operation_name}")
        except Exception as rollback_error:
            logger.error(f"Failed to rollback {operation_name}: {rollback_error}")
        return False
