# Development Standards

## 🎯 **Syfte**

Detta dokument definierar utvecklingsstandarder för att undvika återkommande problem och säkerställa konsistent kodkvalitet.

## 🔧 **Autentisering och Auktorisering**

### **Dependency Standards**

**ANVÄND ALLTID:**
```python
from app.dependencies.auth import CurrentTenantUser, TenantScopedDB

@router.get("/endpoint")
async def my_endpoint(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    # tenant_user.tenant_id är alltid tillgängligt
    # db har redan tenant context satt
```

**ANVÄND ALDRIG:**
```python
# ❌ FELAKTIGT - User har inte tenant_id
current_user: User = Depends(get_current_user)

# ❌ FELAKTIGT - Glöm inte sätta tenant context
db: Session = Depends(get_db)
```

### **Endpoint Kategorier**

1. **Tenant-Scoped Endpoints** (99% av alla endpoints)
   - Använd `CurrentTenantUser` + `TenantScopedDB`
   - Data isoleras automatiskt per tenant

2. **Admin-Only Endpoints**
   - Använd `AdminUser` + `TenantScopedDB`
   - Automatisk permission check

3. **System-Wide Endpoints** (sällsynt)
   - Använd `CurrentUser` + `DatabaseSession`
   - Endast för system-administration

## 📊 **Schema Standards**

### **Base Classes**

**ANVÄND ALLTID rätt base-klass:**

```python
from app.schemas.base import (
    BaseResponseModel,           # För system-wide responses
    TenantScopedResponseModel,   # För tenant-scoped responses  
    BaseCreateModel,             # För create requests
    BaseUpdateModel              # För update requests
)

# ✅ KORREKT
class MyResponse(TenantScopedResponseModel):
    id: str
    name: str
    # tenant_id inkluderas automatiskt
    # UUID konvertering hanteras automatiskt

# ✅ KORREKT  
class MyCreate(BaseCreateModel):
    name: str
    # Validering och konfiguration hanteras automatiskt
```

**ANVÄND ALDRIG:**
```python
# ❌ FELAKTIGT - Ingen UUID hantering
class MyResponse(BaseModel):
    id: str  # Kommer krascha med UUID objekt

# ❌ FELAKTIGT - Duplicerad UUID validator
@field_validator('id', mode='before')
def convert_uuid(cls, v):
    # Detta finns redan i base-klasserna
```

## 🏗️ **Router Standards**

### **CRUD Operations**

**ANVÄND BaseTenantRouter för konsistens:**

```python
from app.routers.base import BaseTenantRouter
from app.dependencies.auth import TenantUserWithDB

class MyRouter(BaseTenantRouter[MyModel, MyCreate, MyUpdate, MyResponse]):
    def __init__(self):
        super().__init__(MyModel)

router = APIRouter()
my_router = MyRouter()

@router.get("/items", response_model=List[MyResponse])
async def list_items(
    db_user: TenantUserWithDB,
    skip: int = 0,
    limit: int = 100
):
    db, tenant_user = db_user
    return my_router.list_items(db, tenant_user, skip, limit)
```

### **Error Handling**

**ANVÄND centraliserad error handling:**

```python
from app.routers.base import handle_database_error

try:
    # Database operation
    pass
except Exception as e:
    raise handle_database_error(e)
```

## 🔄 **Migration Checklist**

När du lägger till nya endpoints:

### ✅ **Checklist för nya endpoints:**

1. **Dependencies**
   - [ ] Använder `CurrentTenantUser` för tenant-scoped data
   - [ ] Använder `TenantScopedDB` för automatisk tenant context
   - [ ] Använder `AdminUser` för admin-only endpoints

2. **Schemas**
   - [ ] Response models ärver från `TenantScopedResponseModel`
   - [ ] Create models ärver från `BaseCreateModel`
   - [ ] Update models ärver från `BaseUpdateModel`
   - [ ] Inga manuella UUID validators

3. **Error Handling**
   - [ ] Använder `handle_database_error()` för DB fel
   - [ ] Konsistenta HTTP status codes
   - [ ] Beskrivande felmeddelanden

4. **Testing**
   - [ ] Testar tenant isolation
   - [ ] Testar permission checks
   - [ ] Testar UUID serialisering

## 🚫 **Vanliga Misstag att Undvika**

### **1. Fel Dependency**
```python
# ❌ FELAKTIGT
current_user: User = Depends(get_current_user)
# User har inte tenant_id!

# ✅ KORREKT
tenant_user: CurrentTenantUser
```

### **2. Glömd Tenant Context**
```python
# ❌ FELAKTIGT
db: Session = Depends(get_db)
# Ingen tenant isolation!

# ✅ KORREKT
db: TenantScopedDB
```

### **3. Manuell UUID Hantering**
```python
# ❌ FELAKTIGT
class MyResponse(BaseModel):
    id: str
    
    @field_validator('id', mode='before')
    def convert_uuid(cls, v):
        # Duplicerad kod!

# ✅ KORREKT
class MyResponse(BaseResponseModel):
    id: str
    # UUID hantering automatisk
```

### **4. Inkonsistent Error Handling**
```python
# ❌ FELAKTIGT
except Exception as e:
    raise HTTPException(500, "Something went wrong")

# ✅ KORREKT
except Exception as e:
    raise handle_database_error(e)
```

## 📝 **Code Review Checklist**

Innan merge, kontrollera:

- [ ] Alla endpoints använder rätt dependencies
- [ ] Alla schemas använder rätt base-klasser
- [ ] Tenant isolation fungerar korrekt
- [ ] UUID serialisering hanteras automatiskt
- [ ] Error handling är konsistent
- [ ] Permissions kontrolleras där det behövs

## 🔄 **Refactoring Existing Code**

För befintlig kod som inte följer standarderna:

1. **Identifiera problemområden**
2. **Uppdatera dependencies först**
3. **Migrera schemas till base-klasser**
4. **Testa tenant isolation**
5. **Uppdatera error handling**

Detta säkerställer att vi inte får samma problem igen!
