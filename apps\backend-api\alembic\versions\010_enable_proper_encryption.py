"""Enable proper encryption with Fernet

Revision ID: 010
Revises: 009
Create Date: 2024-01-20 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '010'
down_revision = '009'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Enable proper encryption using custom Fernet-based TypeDecorator
    # The actual encryption is handled in the application layer via the Encrypted type
    
    # Update comments to reflect proper encryption is now enabled
    op.execute("""
        COMMENT ON COLUMN invoice_integrations.configuration IS 
        'Configuration data - encrypted using Fernet encryption'
    """)
    
    op.execute("""
        COMMENT ON COLUMN oauth2_tokens.access_token IS 
        'Access token - encrypted using Fernet encryption'
    """)
    
    op.execute("""
        COMMENT ON COLUMN oauth2_tokens.refresh_token IS 
        'Refresh token - encrypted using Fernet encryption'
    """)
    
    op.execute("""
        COMMENT ON COLUMN oauth2_tokens.extra_data IS 
        'Extra data - encrypted using Fernet encryption'
    """)


def downgrade() -> None:
    # Remove encryption comments
    op.execute("COMMENT ON COLUMN invoice_integrations.configuration IS NULL")
    op.execute("COMMENT ON COLUMN oauth2_tokens.access_token IS NULL")
    op.execute("COMMENT ON COLUMN oauth2_tokens.refresh_token IS NULL")
    op.execute("COMMENT ON COLUMN oauth2_tokens.extra_data IS NULL")
