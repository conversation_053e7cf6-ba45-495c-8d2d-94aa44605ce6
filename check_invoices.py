#!/usr/bin/env python3
"""
Script to check invoices and sessions in the database
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'apps', 'backend-api'))

from app.database import SessionLocal
from app.models.invoice import Invoice, Session
from sqlalchemy import desc
from datetime import datetime, timed<PERSON><PERSON>

def check_invoices_and_sessions():
    """Check invoices and their sessions"""
    db = SessionLocal()
    
    try:
        print("🔍 Checking Invoices and Sessions")
        print("=" * 50)
        
        # Get all invoices
        invoices = db.query(Invoice).order_by(desc(Invoice.created_at)).limit(10).all()
        
        print(f"\n📄 Recent Invoices: {len(invoices)}")
        for invoice in invoices:
            print(f"  Invoice {invoice.id}")
            print(f"    Number: {invoice.invoice_number}")
            print(f"    Import Type: {invoice.import_typ}")
            print(f"    Status: {invoice.status}")
            print(f"    Created: {invoice.created_at}")
            
            # Check if it has a session
            session = db.query(Session).filter(Session.invoice_id == invoice.id).first()
            if session:
                print(f"    ✅ Has Session: {session.id} (Status: {session.status})")
            else:
                print(f"    ❌ No Session found")
            print()
        
        # Count totals
        total_invoices = db.query(Invoice).count()
        total_sessions = db.query(Session).count()
        
        print(f"📊 Totals:")
        print(f"  Total Invoices: {total_invoices}")
        print(f"  Total Sessions: {total_sessions}")
        print(f"  Invoices without Sessions: {total_invoices - total_sessions}")
        
        # Show invoices without sessions
        invoices_without_sessions = db.query(Invoice).outerjoin(Session).filter(Session.id == None).all()
        
        if invoices_without_sessions:
            print(f"\n❌ Invoices without Sessions ({len(invoices_without_sessions)}):")
            for invoice in invoices_without_sessions:
                print(f"  {invoice.invoice_number} (ID: {invoice.id})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_invoices_and_sessions()
