{"name": "aggie", "version": "1.0.0", "license": "MIT", "scripts": {"build": "nx build", "test": "nx test", "lint": "nx workspace-lint && nx lint", "e2e": "nx e2e", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:build": "nx affected:build", "affected:e2e": "nx affected:e2e", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "update": "nx migrate latest", "workspace-generator": "nx workspace-generator", "dep-graph": "nx dep-graph", "help": "nx help"}, "private": true, "devDependencies": {"@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/js": "21.3.11", "@nx/react": "^20.1.4", "@nx/webpack": "^21.3.11", "@nx/workspace": "21.3.11", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "~9.8.0", "eslint-config-prettier": "^10.0.0", "nx": "21.3.11", "prettier": "^2.6.2", "typescript": "~5.5.2"}, "dependencies": {"@aggie/shared-types": "file:packages/shared-types", "@aggie/ui-components": "file:packages/ui-components", "webpack-dev-server": "^5.2.1"}, "workspaces": ["apps/*", "packages/*"], "resolutions": {"webpack-dev-server": "^5.2.1", "koa": "^3.0.1"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31", "css-select": "^5.1.0", "svgo": "^3.0.0", "koa": "^3.0.1"}}