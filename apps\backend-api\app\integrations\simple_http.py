"""
Simple HTTP invoice integration implementation.
"""

import httpx
import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from urllib.parse import urlparse, urljoin
from pathlib import Path

from .base import InvoiceFetcher


class SimpleHttpInvoiceFetcher(InvoiceFetcher):
    """
    Simple HTTP invoice fetcher implementation.
    
    Handles direct file downloads from HTTP/HTTPS URLs with security validation.
    """
    
    def __init__(self, tenant_id: str, configuration: Dict[str, Any]):
        super().__init__(tenant_id, configuration)
        self.base_url = configuration.get("base_url", "")
        self.auth_type = configuration.get("auth_type", "none")
        self.username = configuration.get("username")
        self.password = configuration.get("password")
        self.api_key = configuration.get("api_key")
        self.headers = configuration.get("headers", {})
        
        # Security settings
        self.allowed_domains = configuration.get("allowed_domains", [])
        self.max_file_size = configuration.get("max_file_size", 50 * 1024 * 1024)  # 50MB
        self.allowed_file_types = configuration.get("allowed_file_types", ["pdf", "png", "jpg", "jpeg"])
        self.max_redirects = configuration.get("max_redirects", 3)
        
    def _validate_url(self, url: str) -> bool:
        """
        Validate URL for security.
        
        Args:
            url: URL to validate
            
        Returns:
            True if URL is safe to access
        """
        try:
            parsed = urlparse(url)
            
            # Must be HTTP or HTTPS
            if parsed.scheme not in ["http", "https"]:
                self.logger.warning(f"Invalid URL scheme: {parsed.scheme}")
                return False
            
            # Check against allowed domains if configured
            if self.allowed_domains:
                if parsed.netloc not in self.allowed_domains:
                    self.logger.warning(f"Domain not in allowed list: {parsed.netloc}")
                    return False
            
            # Prevent access to private networks
            if self._is_private_network(parsed.netloc):
                self.logger.warning(f"Access to private network blocked: {parsed.netloc}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating URL {url}: {e}")
            return False
    
    def _is_private_network(self, hostname: str) -> bool:
        """
        Check if hostname points to a private network.
        
        Args:
            hostname: Hostname to check
            
        Returns:
            True if hostname is in private network range
        """
        import ipaddress
        
        try:
            # Try to resolve hostname to IP
            ip = ipaddress.ip_address(hostname)
            return ip.is_private or ip.is_loopback or ip.is_link_local
        except ValueError:
            # Not an IP address, check for localhost patterns
            localhost_patterns = [
                "localhost", "127.0.0.1", "::1",
                "0.0.0.0", "169.254.", "10.", "172.16.", "192.168."
            ]
            return any(pattern in hostname.lower() for pattern in localhost_patterns)
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers based on auth type.
        
        Returns:
            Dictionary of authentication headers
        """
        auth_headers = {}
        
        if self.auth_type == "basic" and self.username and self.password:
            import base64
            credentials = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            auth_headers["Authorization"] = f"Basic {credentials}"
        elif self.auth_type == "bearer" and self.api_key:
            auth_headers["Authorization"] = f"Bearer {self.api_key}"
        elif self.auth_type == "api_key" and self.api_key:
            auth_headers["X-API-Key"] = self.api_key
        
        # Add custom headers
        auth_headers.update(self.headers)
        
        return auth_headers
    
    async def fetch_invoices(self, since: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch invoices from HTTP endpoint.
        
        Args:
            since: ISO timestamp to fetch invoices since (may not be supported)
            
        Returns:
            List of standardized invoice data
        """
        try:
            if not self.base_url:
                raise ValueError("Base URL not configured for HTTP integration")
            
            if not self._validate_url(self.base_url):
                raise ValueError(f"Invalid or unsafe URL: {self.base_url}")
            
            headers = self._get_auth_headers()
            headers.update({
                "User-Agent": "Aggie-Invoice-Processor/1.0",
                "Accept": "application/json, text/html, application/pdf, image/*"
            })
            
            async with httpx.AsyncClient(
                headers=headers,
                timeout=30.0,
                follow_redirects=True,
                max_redirects=self.max_redirects
            ) as client:
                
                self.logger.info(f"Fetching invoices from HTTP endpoint: {self.base_url}")
                
                response = await client.get(self.base_url)
                response.raise_for_status()
                
                # Check content type to determine how to process response
                content_type = response.headers.get("content-type", "").lower()
                
                if "application/json" in content_type:
                    return await self._process_json_response(response.json())
                elif "text/html" in content_type:
                    return await self._process_html_response(response.text, self.base_url)
                elif any(ct in content_type for ct in ["application/pdf", "image/"]):
                    return await self._process_file_response(response, self.base_url)
                else:
                    # Try to process as file
                    return await self._process_file_response(response, self.base_url)
                
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                await self._handle_rate_limit(60)
                raise
            else:
                self.logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
                raise
        except Exception as e:
            self._log_error("fetch_invoices", e, {"base_url": self.base_url, "since": since})
            raise
    
    async def _process_json_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process JSON response containing invoice data.
        
        Args:
            data: JSON response data
            
        Returns:
            List of standardized invoice data
        """
        invoices = []
        
        # Handle different JSON structures
        if isinstance(data, list):
            # Direct list of invoices
            for item in data:
                invoice = await self._process_json_invoice(item)
                if invoice:
                    invoices.append(invoice)
        elif isinstance(data, dict):
            # Check for common wrapper keys
            for key in ["invoices", "data", "items", "files"]:
                if key in data and isinstance(data[key], list):
                    for item in data[key]:
                        invoice = await self._process_json_invoice(item)
                        if invoice:
                            invoices.append(invoice)
                    break
            else:
                # Single invoice object
                invoice = await self._process_json_invoice(data)
                if invoice:
                    invoices.append(invoice)
        
        return invoices
    
    async def _process_json_invoice(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process a single JSON invoice item.
        
        Args:
            item: JSON invoice data
            
        Returns:
            Standardized invoice data or None
        """
        try:
            # Extract file URL
            file_url = None
            for url_key in ["url", "file_url", "download_url", "link", "href"]:
                if url_key in item:
                    file_url = item[url_key]
                    break
            
            if not file_url:
                return None
            
            # Make URL absolute if relative
            if file_url.startswith("/"):
                file_url = urljoin(self.base_url, file_url)
            
            if not self._validate_url(file_url):
                return None
            
            invoice_data = {
                "external_id": item.get("id", f"http_{hash(file_url)}"),
                "invoice_number": item.get("invoice_number", item.get("name", Path(file_url).stem)),
                "supplier_name": item.get("supplier_name", item.get("supplier", "Unknown Supplier")),
                "invoice_date": item.get("invoice_date", item.get("date")),
                "due_date": item.get("due_date"),
                "total_amount": item.get("total_amount", item.get("amount")),
                "currency": item.get("currency", "SEK"),
                "file_url": file_url,
                "file_type": self._determine_file_type(file_url),
                "metadata": {
                    "http_source": self.base_url,
                    "original_data": item
                }
            }
            
            return self._standardize_invoice_data(invoice_data)
            
        except Exception as e:
            self.logger.error(f"Error processing JSON invoice item: {e}")
            return None
    
    async def _process_html_response(self, html: str, base_url: str) -> List[Dict[str, Any]]:
        """
        Process HTML response and extract file links.
        
        Args:
            html: HTML content
            base_url: Base URL for resolving relative links
            
        Returns:
            List of standardized invoice data
        """
        invoices = []
        
        # Simple regex to find file links
        # This is basic - could be enhanced with proper HTML parsing
        link_pattern = r'href=["\']([^"\']+\.(?:pdf|png|jpg|jpeg))["\']'
        matches = re.findall(link_pattern, html, re.IGNORECASE)
        
        for match in matches:
            try:
                file_url = urljoin(base_url, match)
                
                if not self._validate_url(file_url):
                    continue
                
                invoice_data = {
                    "external_id": f"http_{hash(file_url)}",
                    "invoice_number": Path(file_url).stem,
                    "supplier_name": "Unknown Supplier",
                    "invoice_date": None,
                    "due_date": None,
                    "total_amount": None,
                    "currency": "SEK",
                    "file_url": file_url,
                    "file_type": self._determine_file_type(file_url),
                    "metadata": {
                        "http_source": base_url,
                        "extracted_from_html": True
                    }
                }
                
                invoices.append(self._standardize_invoice_data(invoice_data))
                
            except Exception as e:
                self.logger.error(f"Error processing HTML link {match}: {e}")
                continue
        
        return invoices
    
    async def _process_file_response(self, response: httpx.Response, url: str) -> List[Dict[str, Any]]:
        """
        Process direct file response.
        
        Args:
            response: HTTP response containing file
            url: File URL
            
        Returns:
            List with single standardized invoice data
        """
        try:
            # Check file size
            content_length = response.headers.get("content-length")
            if content_length and int(content_length) > self.max_file_size:
                raise ValueError(f"File too large: {content_length} bytes")
            
            file_type = self._determine_file_type(url)
            if file_type not in self.allowed_file_types:
                raise ValueError(f"File type not allowed: {file_type}")
            
            invoice_data = {
                "external_id": f"http_{hash(url)}",
                "invoice_number": Path(url).stem,
                "supplier_name": "Unknown Supplier",
                "invoice_date": None,
                "due_date": None,
                "total_amount": None,
                "currency": "SEK",
                "file_url": url,
                "file_type": file_type,
                "metadata": {
                    "http_source": self.base_url,
                    "content_length": content_length,
                    "content_type": response.headers.get("content-type")
                }
            }
            
            return [self._standardize_invoice_data(invoice_data)]
            
        except Exception as e:
            self.logger.error(f"Error processing file response from {url}: {e}")
            return []
    
    def _determine_file_type(self, url: str) -> str:
        """Determine file type from URL"""
        try:
            path = Path(urlparse(url).path)
            extension = path.suffix.lower().lstrip(".")
            if extension in self.allowed_file_types:
                return extension
        except Exception:
            pass
        return "pdf"
    
    async def post_processing_callback(self, invoice_id: str, status: str, data: Dict[str, Any] = None) -> bool:
        """
        HTTP integrations typically don't support callbacks.
        
        Args:
            invoice_id: External invoice ID
            status: Processing status
            data: Additional processing data
            
        Returns:
            True (always successful for HTTP)
        """
        self.logger.info(f"HTTP invoice {invoice_id} processing completed with status: {status}")
        return True
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        Test connection to HTTP endpoint.
        
        Returns:
            Connection test results
        """
        try:
            if not self.base_url:
                return {
                    "success": False,
                    "message": "Base URL not configured",
                    "details": {"tested_at": datetime.utcnow().isoformat()}
                }
            
            if not self._validate_url(self.base_url):
                return {
                    "success": False,
                    "message": f"Invalid or unsafe URL: {self.base_url}",
                    "details": {"tested_at": datetime.utcnow().isoformat()}
                }
            
            headers = self._get_auth_headers()
            headers["User-Agent"] = "Aggie-Invoice-Processor/1.0"
            
            async with httpx.AsyncClient(headers=headers, timeout=10.0) as client:
                response = await client.head(self.base_url)
                response.raise_for_status()
                
                return {
                    "success": True,
                    "message": "Successfully connected to HTTP endpoint",
                    "details": {
                        "url": self.base_url,
                        "status_code": response.status_code,
                        "content_type": response.headers.get("content-type"),
                        "tested_at": datetime.utcnow().isoformat()
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to connect to HTTP endpoint: {str(e)}",
                "details": {
                    "url": self.base_url,
                    "error_type": type(e).__name__,
                    "tested_at": datetime.utcnow().isoformat()
                }
            }
