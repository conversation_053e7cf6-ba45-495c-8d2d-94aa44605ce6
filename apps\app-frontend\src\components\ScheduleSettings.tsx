import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  ClockIcon,
  PauseIcon,
  Cog6ToothIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { integrationsApi } from '../services/api';
import toast from 'react-hot-toast';

interface ScheduleSettingsProps {
  className?: string;
}

export default function ScheduleSettings({ className = '' }: ScheduleSettingsProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [customCron, setCustomCron] = useState('');
  const queryClient = useQueryClient();

  const { data: scheduleSettings, isLoading } = useQuery(
    'schedule-settings',
    integrationsApi.getScheduleSettings
  );

  const updateMutation = useMutation(integrationsApi.updateScheduleSettings, {
    onSuccess: () => {
      queryClient.invalidateQueries('schedule-settings');
      toast.success('Schedule settings updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to update schedule settings');
    },
  });

  const handleToggleSchedule = () => {
    if (scheduleSettings) {
      updateMutation.mutate({
        enabled: !scheduleSettings.enabled
      });
    }
  };

  const handleUpdateCron = (cronExpression: string) => {
    updateMutation.mutate({
      cron_expression: cronExpression
    });
    setShowAdvanced(false);
  };

  const handleCustomCronSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (customCron.trim()) {
      handleUpdateCron(customCron.trim());
      setCustomCron('');
    }
  };

  const predefinedSchedules = [
    { label: 'Every 30 minutes', cron: '*/30 * * * *', description: 'Runs every 30 minutes' },
    { label: 'Every hour', cron: '0 * * * *', description: 'Runs at the top of every hour' },
    { label: 'Every 2 hours', cron: '0 */2 * * *', description: 'Runs every 2 hours' },
    { label: 'Every 4 hours', cron: '0 */4 * * *', description: 'Runs every 4 hours' },
    { label: 'Every 6 hours', cron: '0 */6 * * *', description: 'Runs every 6 hours' },
    { label: 'Daily at 9 AM', cron: '0 9 * * *', description: 'Runs daily at 9:00 AM' },
    { label: 'Daily at 6 PM', cron: '0 18 * * *', description: 'Runs daily at 6:00 PM' },
    { label: 'Weekdays at 9 AM', cron: '0 9 * * 1-5', description: 'Runs Monday to Friday at 9:00 AM' }
  ];

  const getCurrentScheduleLabel = () => {
    if (!scheduleSettings?.cron_expression) return 'Unknown';
    
    const match = predefinedSchedules.find(s => s.cron === scheduleSettings.cron_expression);
    return match ? match.label : `Custom: ${scheduleSettings.cron_expression}`;
  };

  if (isLoading) {
    return (
      <div className="card-container">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="card-container">
        <h3 className="section-title flex items-center mb-6">
          <ClockIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Scheduled Sync
        </h3>

        <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-2xl p-6">
          {/* Schedule Toggle */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Automatic Invoice Sync</h4>
              <p className="text-sm text-gray-600">
                Automatically fetch invoices from your integrations on a schedule
              </p>
            </div>
            <button
              onClick={handleToggleSchedule}
              disabled={updateMutation.isLoading}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 ${
                scheduleSettings?.enabled ? 'bg-indigo-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  scheduleSettings?.enabled ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>

          {/* Current Schedule Info */}
          {scheduleSettings?.enabled && (
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-2">
                <ClockIcon className="h-5 w-5 text-indigo-600" />
                <span className="font-medium text-gray-900">Current Schedule:</span>
                <span className="text-gray-700">{getCurrentScheduleLabel()}</span>
              </div>
              
              {scheduleSettings.last_run && (
                <p className="text-sm text-gray-600">
                  Last run: {new Date(scheduleSettings.last_run).toLocaleString()}
                </p>
              )}
              
              {scheduleSettings.next_run && (
                <p className="text-sm text-gray-600">
                  Next run: {new Date(scheduleSettings.next_run).toLocaleString()}
                </p>
              )}
            </div>
          )}

          {/* Schedule Configuration */}
          {scheduleSettings?.enabled && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h5 className="font-medium text-gray-900">Schedule Configuration</h5>
                <button
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="btn-small flex items-center"
                >
                  <Cog6ToothIcon className="h-4 w-4 mr-2" />
                  {showAdvanced ? 'Hide Advanced' : 'Advanced'}
                </button>
              </div>

              {!showAdvanced ? (
                /* Predefined Schedules */
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {predefinedSchedules.map((schedule) => (
                    <button
                      key={schedule.cron}
                      onClick={() => handleUpdateCron(schedule.cron)}
                      disabled={updateMutation.isLoading}
                      className={`text-left p-3 rounded-lg border transition-colors ${
                        scheduleSettings.cron_expression === schedule.cron
                          ? 'border-indigo-500 bg-indigo-50 text-indigo-900'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="font-medium">{schedule.label}</div>
                      <div className="text-sm text-gray-600">{schedule.description}</div>
                    </button>
                  ))}
                </div>
              ) : (
                /* Custom Cron Expression */
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                      <div className="text-sm text-blue-700">
                        <p className="font-medium mb-1">Cron Expression Format:</p>
                        <p className="font-mono text-xs">minute hour day month day-of-week</p>
                        <p className="mt-2">
                          Examples: <code>0 */2 * * *</code> (every 2 hours), 
                          <code>30 9 * * 1-5</code> (9:30 AM weekdays)
                        </p>
                      </div>
                    </div>
                  </div>

                  <form onSubmit={handleCustomCronSubmit} className="flex space-x-3">
                    <input
                      type="text"
                      value={customCron}
                      onChange={(e) => setCustomCron(e.target.value)}
                      placeholder="0 */2 * * *"
                      className="input-custom flex-1 font-mono"
                    />
                    <button
                      type="submit"
                      disabled={updateMutation.isLoading || !customCron.trim()}
                      className="btn-primary"
                    >
                      Apply
                    </button>
                  </form>

                  <div className="text-sm text-gray-600">
                    Current: <code className="bg-gray-100 px-2 py-1 rounded">
                      {scheduleSettings.cron_expression}
                    </code>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Disabled State */}
          {!scheduleSettings?.enabled && (
            <div className="text-center py-8">
              <PauseIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">Scheduled sync is disabled</p>
              <p className="text-sm text-gray-400">
                Enable automatic sync to fetch invoices from your integrations on a regular schedule
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
