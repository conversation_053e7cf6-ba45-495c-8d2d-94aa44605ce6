"""Enable RLS for sessions and session_logs tables

Revision ID: 012_enable_rls_for_sessions
Revises: 011_create_new_invoice_session_system
Create Date: 2025-08-05 17:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '012'
down_revision = '011'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Enable RLS on sessions and session_logs tables
    op.execute("ALTER TABLE sessions ENABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE session_logs ENABLE ROW LEVEL SECURITY")
    
    # Create RLS policies for sessions
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON sessions
        FOR ALL
        TO PUBLIC
        USING (tenant_id::text = current_setting('app.current_tenant_id', true))
        WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
    """)
    
    # Create RLS policies for session_logs
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON session_logs
        FOR ALL
        TO PUBLIC
        USING (tenant_id::text = current_setting('app.current_tenant_id', true))
        WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
    """)
    
    # Create additional indexes for better performance with RLS
    op.create_index('idx_sessions_tenant_status', 'sessions', ['tenant_id', 'status'])
    op.create_index('idx_session_logs_tenant_session', 'session_logs', ['tenant_id', 'session_id'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_sessions_tenant_status', 'sessions')
    op.drop_index('idx_session_logs_tenant_session', 'session_logs')
    
    # Drop RLS policies
    op.execute("DROP POLICY IF EXISTS tenant_isolation_policy ON sessions")
    op.execute("DROP POLICY IF EXISTS tenant_isolation_policy ON session_logs")
    
    # Disable RLS
    op.execute("ALTER TABLE sessions DISABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE session_logs DISABLE ROW LEVEL SECURITY")
