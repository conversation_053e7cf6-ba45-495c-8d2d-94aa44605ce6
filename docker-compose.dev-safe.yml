version: '3.8'

services:
  backend-api:
    build:
      context: ./apps/backend-api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/aggie_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET_KEY=dev-secret-key-change-in-production
      - ENCRYPTION_KEY=dev-encryption-key-32-chars-long
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LLM_PROVIDER=openai
      - ENVIRONMENT=development
      # Python development optimizations
      - PYTHONDONTWRITEBYTECODE=1  # Prevent .pyc files
      - PYTHONUNBUFFERED=1         # Better logging output
      - PYTHONPATH=/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps/backend-api:/app
      - ./uploads:/app/uploads
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    # Use polling instead of file watching to avoid memory issues
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-delay 2 --use-colors --log-level info
    # Override user to root for development (easier file permissions)
    user: root

  # Include other services from main docker-compose.yml
  db:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: aggie_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
