"""
Custom email validator using regex patterns.
Provides robust email validation without external dependencies.
"""

import re
from typing import Optional


class EmailValidator:
    """
    A simple but robust email validator using regex patterns.
    
    This validator checks for:
    - Basic email format (local@domain)
    - Valid characters in local and domain parts
    - Proper domain structure
    - Common email patterns
    """
    
    # RFC 5322 compliant email regex (simplified but robust)
    EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    # More strict pattern for production use
    STRICT_EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?@[a-zA-Z0-9]([a-zA-Z0-9.-]*[a-zA-Z0-9])?\.[a-zA-Z]{2,}$'
    )
    
    # Common disposable email domains to block (optional)
    DISPOSABLE_DOMAINS = {
        '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
        'mailinator.com', 'yopmail.com', 'temp-mail.org'
    }
    
    @classmethod
    def is_valid(cls, email: str, strict: bool = False) -> bool:
        """
        Validate email address format.
        
        Args:
            email: Email address to validate
            strict: Use stricter validation rules
            
        Returns:
            True if email is valid, False otherwise
        """
        if not email or not isinstance(email, str):
            return False
            
        # Remove whitespace
        email = email.strip()
        
        # Check length (RFC 5321 limits)
        if len(email) > 254:  # Total length limit
            return False
            
        # Split local and domain parts
        try:
            local, domain = email.rsplit('@', 1)
        except ValueError:
            return False
            
        # Check local part length (64 characters max)
        if len(local) > 64:
            return False
            
        # Choose pattern based on strictness
        pattern = cls.STRICT_EMAIL_PATTERN if strict else cls.EMAIL_PATTERN
        
        return bool(pattern.match(email))
    
    @classmethod
    def is_disposable(cls, email: str) -> bool:
        """
        Check if email uses a disposable email service.
        
        Args:
            email: Email address to check
            
        Returns:
            True if email uses disposable domain, False otherwise
        """
        if not cls.is_valid(email):
            return False
            
        domain = email.split('@')[1].lower()
        return domain in cls.DISPOSABLE_DOMAINS
    
    @classmethod
    def validate_and_normalize(cls, email: str, strict: bool = False) -> Optional[str]:
        """
        Validate email and return normalized version.
        
        Args:
            email: Email address to validate
            strict: Use stricter validation rules
            
        Returns:
            Normalized email if valid, None if invalid
        """
        if not cls.is_valid(email, strict=strict):
            return None
            
        # Normalize: lowercase domain, preserve local case
        local, domain = email.rsplit('@', 1)
        return f"{local}@{domain.lower()}"


def validate_email(email: str, strict: bool = False) -> bool:
    """
    Convenience function for email validation.
    
    Args:
        email: Email address to validate
        strict: Use stricter validation rules
        
    Returns:
        True if email is valid, False otherwise
    """
    return EmailValidator.is_valid(email, strict=strict)


def normalize_email(email: str, strict: bool = False) -> Optional[str]:
    """
    Convenience function for email validation and normalization.
    
    Args:
        email: Email address to validate and normalize
        strict: Use stricter validation rules
        
    Returns:
        Normalized email if valid, None if invalid
    """
    return EmailValidator.validate_and_normalize(email, strict=strict)


# Pydantic validator function
def pydantic_email_validator(email: str) -> str:
    """
    Pydantic validator function for email fields.
    
    Args:
        email: Email address to validate
        
    Returns:
        Normalized email if valid
        
    Raises:
        ValueError: If email is invalid
    """
    normalized = normalize_email(email, strict=True)
    if normalized is None:
        raise ValueError("Invalid email address format")
    return normalized
