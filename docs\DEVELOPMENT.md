# Development Guide - Hot Reload Setup

Detta dokument beskriver hur du använder den nya development environment med hot reload för Aggie-projektet.

## 🚀 Snabbstart

### Starta Development Environment

```bash
# Med Docker Compose (rekommenderat)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# I bakgrunden
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build

# Med Make (om tillgängligt)
make dev-up
```

### Stoppa Development Environment

```bash
# Stoppa alla services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

# Med Make
make dev-down
```

## 📁 Projektstruktur

```
aggie/
├── apps/
│   ├── admin-frontend/     # Admin console (port 3001)
│   ├── app-frontend/       # User app (port 3002)
│   └── backend-api/        # FastAPI backend (port 8000)
├── packages/
│   ├── shared-types/       # Delade TypeScript typer
│   └── ui-components/      # Delade UI komponenter
├── docker-compose.yml      # Bas konfiguration
├── docker-compose.dev.yml  # Development overrides
└── Makefile               # Convenience commands
```

## 🔥 Hot Reload Funktioner

### Frontend (React)
- **Automatisk reload**: Ändringar i `.tsx`, `.ts`, `.css` filer triggar ombyggnad
- **Fast Refresh**: Bevarar komponentstate vid ändringar
- **Source Maps**: Aktiverade för bättre debugging
- **ESLint integration**: Live linting i development

### Backend (FastAPI)
- **Uvicorn reload**: Automatisk restart vid Python-filändringar
- **WatchFiles**: Övervakar hela `/app` mappen
- **Debug logging**: Utökad loggning i development mode
- **Hot reload för alla moduler**: Inkluderar routers, models, etc.

### Shared Packages
- **Symlinks**: Automatiska länkar till shared packages
- **Live updates**: Ändringar i packages reflekteras direkt i apps

## 🛠️ Användbara Kommandon

### Loggar
```bash
# Alla services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f

# Endast backend
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f backend-api worker scheduler

# Endast frontend
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f admin-frontend app-frontend

# Med Make
make dev-logs
make dev-logs-backend
make dev-logs-frontend
```

### Restart Services
```bash
# Alla services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart

# Endast backend
docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart backend-api worker scheduler

# Endast frontend
docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart admin-frontend app-frontend

# Med Make
make dev-restart
make dev-restart-backend
make dev-restart-frontend
```

### Shell Access
```bash
# Backend container
docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend-api bash

# Frontend containers
docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec admin-frontend sh
docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec app-frontend sh

# Med Make
make dev-shell
make dev-shell-admin
make dev-shell-app
```

## 🌐 Tillgängliga URLs

- **Admin Frontend**: http://localhost:3001
- **App Frontend**: http://localhost:3002
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5432 (postgres/postgres)
- **Redis**: localhost:6379

## 🔧 Development Optimeringar

### Environment Variables
Development environment inkluderar följande optimeringar:

**Frontend:**
- `CHOKIDAR_USEPOLLING=true` - Bättre file watching i Docker
- `FAST_REFRESH=true` - React Fast Refresh aktiverat
- `GENERATE_SOURCEMAP=true` - Source maps för debugging
- `WDS_SOCKET_HOST=localhost` - WebSocket konfiguration

**Backend:**
- `PYTHONDONTWRITEBYTECODE=1` - Förhindrar .pyc filer
- `PYTHONUNBUFFERED=1` - Bättre logging output
- `PYTHONPATH=/app` - Korrekt Python path

### Volume Mounts
- Source kod mappas direkt för omedelbar reflektion av ändringar
- `node_modules` exkluderas från host för bättre prestanda
- Shared packages länkas automatiskt

## 🐛 Troubleshooting

### Hot Reload fungerar inte

**Frontend:**
1. Kontrollera att `CHOKIDAR_USEPOLLING=true` är satt
2. Verifiera att volume mounts är korrekta
3. Restart frontend service: `make dev-restart-frontend`

**Backend:**
1. Kontrollera att `--reload` flaggan är aktiv i logs
2. Verifiera att filen är inom `/app` mappen
3. Restart backend service: `make dev-restart-backend`

### Långsam prestanda
1. Kontrollera att `node_modules` är exkluderade från volume mounts
2. Överväg att öka Docker Desktop minne allocation
3. På Windows: Använd WSL2 för bättre prestanda

### Port konflikter
1. Kontrollera att portarna 3001, 3002, 8000 är lediga
2. Stoppa andra Docker containers: `docker stop $(docker ps -q)`
3. Ändra portar i `docker-compose.dev.yml` om nödvändigt

### Permission errors
1. På Linux/Mac: Kontrollera file permissions
2. I development: Services körs som root för enklare file access
3. Restart med: `docker-compose down && docker-compose up --build`

## 📝 Best Practices

1. **Använd alltid development compose**: `-f docker-compose.yml -f docker-compose.dev.yml`
2. **Övervaka loggar**: Håll `make dev-logs` öppet i en terminal
3. **Rebuild vid package ändringar**: Kör `--build` efter npm install
4. **Testa i production mode**: Använd `docker-compose.prod.yml` innan deploy
5. **Backup data**: Database data sparas i Docker volumes

## 🔄 Workflow Exempel

```bash
# 1. Starta development environment
make dev-up

# 2. Öppna loggar i separat terminal
make dev-logs

# 3. Gör ändringar i kod
# Hot reload sker automatiskt!

# 4. Vid behov, restart specifik service
make dev-restart-frontend

# 5. Stoppa när du är klar
make dev-down
```

## 📚 Relaterad Dokumentation

- [Architecture Overview](./ARCHITECTURE.md)
- [API Documentation](./API.md)
- [Deployment Guide](./DEPLOYMENT.md)
