{"name": "ui-components", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/ui-components/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/ui-components", "main": "packages/ui-components/src/index.ts", "tsConfig": "packages/ui-components/tsconfig.lib.json", "assets": ["packages/ui-components/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/ui-components/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/ui-components/jest.config.ts", "passWithNoTests": true}}}, "tags": ["scope:shared", "type:lib"]}