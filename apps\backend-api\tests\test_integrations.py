"""
Tests for invoice integration system.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from uuid import uuid4

from app.integrations.base import InvoiceFetcher
from app.integrations.fortnox import FortnoxInvoiceFetcher
from app.integrations.visma import VismaInvoiceFetcher
from app.integrations.simple_http import SimpleHttpInvoiceFetcher
from app.utils.oauth2 import OAuth2Manager, FortnoxOAuth2Provider, VismaOAuth2Provider
from app.services.integration_service import IntegrationService
from app.models.integration import InvoiceIntegration, OAuth2Token


class TestInvoiceFetcherBase:
    """Test the abstract base class"""
    
    def test_abstract_methods(self):
        """Test that abstract methods raise NotImplementedError"""
        with pytest.raises(TypeError):
            InvoiceFetcher("tenant-id", {})


class TestFortnoxInvoiceFetcher:
    """Test Fortnox integration"""
    
    @pytest.fixture
    def mock_db(self):
        return Mock()
    
    @pytest.fixture
    def fortnox_config(self):
        return {
            "integration_id": str(uuid4()),
            "client_id": "test-client-id",
            "client_secret": "test-client-secret"
        }
    
    @pytest.fixture
    def fortnox_fetcher(self, mock_db, fortnox_config):
        return FortnoxInvoiceFetcher("tenant-123", fortnox_config, mock_db)
    
    @pytest.mark.asyncio
    async def test_fetch_invoices_success(self, fortnox_fetcher):
        """Test successful invoice fetching from Fortnox"""
        mock_response_data = {
            "Inbox": [
                {
                    "Id": "123",
                    "Name": "invoice-001.pdf",
                    "Supplier": "Test Supplier",
                    "Date": "2024-01-15",
                    "Path": "/inbox/123",
                    "Size": 12345
                }
            ]
        }
        
        with patch.object(fortnox_fetcher, '_get_authenticated_client') as mock_client:
            mock_http_client = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_http_client
            
            # Mock inbox list response
            mock_http_client.get.return_value.json.return_value = mock_response_data
            mock_http_client.get.return_value.raise_for_status.return_value = None
            
            invoices = await fortnox_fetcher.fetch_invoices()
            
            assert len(invoices) == 1
            assert invoices[0]["external_id"] == "123"
            assert invoices[0]["supplier_name"] == "Test Supplier"
            assert invoices[0]["currency"] == "SEK"
    
    @pytest.mark.asyncio
    async def test_fetch_invoices_authentication_error(self, fortnox_fetcher):
        """Test handling of authentication errors"""
        with patch.object(fortnox_fetcher, '_get_authenticated_client') as mock_client:
            mock_client.side_effect = ValueError("No valid OAuth2 token found")
            
            with pytest.raises(ValueError, match="No valid OAuth2 token found"):
                await fortnox_fetcher.fetch_invoices()
    
    @pytest.mark.asyncio
    async def test_test_connection_success(self, fortnox_fetcher):
        """Test successful connection test"""
        mock_company_data = {
            "CompanyInformation": {
                "CompanyName": "Test Company AB",
                "OrganizationNumber": "556123-4567"
            }
        }
        
        with patch.object(fortnox_fetcher, '_get_authenticated_client') as mock_client:
            mock_http_client = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_http_client
            
            mock_http_client.get.return_value.json.return_value = mock_company_data
            mock_http_client.get.return_value.raise_for_status.return_value = None
            
            result = await fortnox_fetcher.test_connection()
            
            assert result["success"] is True
            assert "Test Company AB" in result["details"]["company_name"]


class TestVismaInvoiceFetcher:
    """Test Visma eEkonomi integration"""
    
    @pytest.fixture
    def mock_db(self):
        return Mock()
    
    @pytest.fixture
    def visma_config(self):
        return {
            "integration_id": str(uuid4()),
            "client_id": "test-client-id",
            "client_secret": "test-client-secret"
        }
    
    @pytest.fixture
    def visma_fetcher(self, mock_db, visma_config):
        return VismaInvoiceFetcher("tenant-123", visma_config, mock_db)
    
    @pytest.mark.asyncio
    async def test_fetch_attachments(self, visma_fetcher):
        """Test fetching attachments from Visma"""
        mock_attachments_data = {
            "Data": [
                {
                    "Id": "att-123",
                    "Name": "invoice-001.pdf",
                    "SupplierName": "Test Supplier",
                    "Created": "2024-01-15T10:00:00Z",
                    "Size": 12345
                }
            ]
        }
        
        with patch.object(visma_fetcher, '_get_authenticated_client') as mock_client:
            mock_http_client = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_http_client
            
            mock_http_client.get.return_value.json.return_value = mock_attachments_data
            mock_http_client.get.return_value.raise_for_status.return_value = None
            
            attachments = await visma_fetcher._fetch_attachments(mock_http_client)
            
            assert len(attachments) == 1
            assert attachments[0]["external_id"] == "visma_attachment_att-123"
            assert attachments[0]["supplier_name"] == "Test Supplier"
    
    @pytest.mark.asyncio
    async def test_fetch_supplier_invoice_drafts(self, visma_fetcher):
        """Test fetching supplier invoice drafts from Visma"""
        mock_drafts_data = {
            "Data": [
                {
                    "Id": "draft-456",
                    "InvoiceNumber": "INV-2024-001",
                    "SupplierName": "Test Supplier",
                    "InvoiceDate": "2024-01-15",
                    "DueDate": "2024-02-15",
                    "TotalAmount": 1500.00,
                    "Currency": "SEK"
                }
            ]
        }
        
        with patch.object(visma_fetcher, '_get_authenticated_client') as mock_client:
            mock_http_client = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_http_client
            
            mock_http_client.get.return_value.json.return_value = mock_drafts_data
            mock_http_client.get.return_value.raise_for_status.return_value = None
            
            drafts = await visma_fetcher._fetch_supplier_invoice_drafts(mock_http_client)
            
            assert len(drafts) == 1
            assert drafts[0]["external_id"] == "visma_draft_draft-456"
            assert drafts[0]["invoice_number"] == "INV-2024-001"
            assert drafts[0]["total_amount"] == 1500.00


class TestSimpleHttpInvoiceFetcher:
    """Test Simple HTTP integration"""
    
    @pytest.fixture
    def http_config(self):
        return {
            "base_url": "https://example.com/invoices",
            "auth_type": "none",
            "allowed_domains": ["example.com"],
            "max_file_size": 10 * 1024 * 1024,
            "allowed_file_types": ["pdf", "png", "jpg"]
        }
    
    @pytest.fixture
    def http_fetcher(self, http_config):
        return SimpleHttpInvoiceFetcher("tenant-123", http_config)
    
    def test_validate_url_success(self, http_fetcher):
        """Test URL validation with valid URLs"""
        assert http_fetcher._validate_url("https://example.com/invoices") is True
        assert http_fetcher._validate_url("http://example.com/files") is True
    
    def test_validate_url_invalid_scheme(self, http_fetcher):
        """Test URL validation with invalid schemes"""
        assert http_fetcher._validate_url("ftp://example.com/files") is False
        assert http_fetcher._validate_url("file:///etc/passwd") is False
    
    def test_validate_url_private_network(self, http_fetcher):
        """Test URL validation blocks private networks"""
        assert http_fetcher._validate_url("http://localhost/files") is False
        assert http_fetcher._validate_url("http://127.0.0.1/files") is False
        assert http_fetcher._validate_url("http://***********/files") is False
    
    def test_validate_url_domain_restriction(self, http_fetcher):
        """Test URL validation with domain restrictions"""
        assert http_fetcher._validate_url("https://example.com/files") is True
        assert http_fetcher._validate_url("https://other.com/files") is False
    
    @pytest.mark.asyncio
    async def test_fetch_invoices_json_response(self, http_fetcher):
        """Test fetching invoices from JSON response"""
        mock_json_data = {
            "invoices": [
                {
                    "id": "inv-001",
                    "invoice_number": "INV-2024-001",
                    "supplier_name": "Test Supplier",
                    "url": "https://example.com/files/inv-001.pdf"
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.headers = {"content-type": "application/json"}
            mock_response.json.return_value = mock_json_data
            mock_response.raise_for_status.return_value = None
            
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            invoices = await http_fetcher.fetch_invoices()
            
            assert len(invoices) == 1
            assert invoices[0]["external_id"] == "inv-001"
            assert invoices[0]["invoice_number"] == "INV-2024-001"
            assert invoices[0]["file_url"] == "https://example.com/files/inv-001.pdf"


class TestOAuth2Manager:
    """Test OAuth2 utilities"""
    
    @pytest.fixture
    def mock_db(self):
        return Mock()
    
    @pytest.fixture
    def oauth_manager(self, mock_db):
        return OAuth2Manager(mock_db)
    
    def test_generate_state(self, oauth_manager):
        """Test state parameter generation"""
        integration_id = str(uuid4())
        state = oauth_manager.generate_state(integration_id)
        
        assert isinstance(state, str)
        assert len(state) > 0
        assert oauth_manager.verify_state(state, integration_id) is True
    
    def test_verify_state_invalid(self, oauth_manager):
        """Test state verification with invalid state"""
        integration_id = str(uuid4())
        invalid_state = "invalid-state"
        
        assert oauth_manager.verify_state(invalid_state, integration_id) is False
    
    def test_get_fortnox_provider(self, oauth_manager):
        """Test getting Fortnox OAuth2 provider"""
        with patch('app.config.settings') as mock_settings:
            mock_settings.fortnox_client_id = "test-id"
            mock_settings.fortnox_client_secret = "test-secret"
            
            provider = oauth_manager.get_provider("FORTNOX")
            
            assert isinstance(provider, FortnoxOAuth2Provider)
            assert provider.client_id == "test-id"
            assert provider.client_secret == "test-secret"
    
    def test_get_visma_provider(self, oauth_manager):
        """Test getting Visma OAuth2 provider"""
        with patch('app.config.settings') as mock_settings:
            mock_settings.visma_client_id = "test-id"
            mock_settings.visma_client_secret = "test-secret"
            
            provider = oauth_manager.get_provider("VISMA")
            
            assert isinstance(provider, VismaOAuth2Provider)
            assert provider.client_id == "test-id"
            assert provider.client_secret == "test-secret"


class TestIntegrationService:
    """Test integration service"""
    
    @pytest.fixture
    def mock_db(self):
        return Mock()
    
    @pytest.fixture
    def integration_service(self, mock_db):
        return IntegrationService(mock_db)
    
    def test_get_active_integrations(self, integration_service, mock_db):
        """Test getting active integrations for tenant"""
        tenant_id = str(uuid4())
        
        mock_db.query.return_value.filter.return_value.all.return_value = [
            Mock(id=uuid4(), integration_type="FORTNOX", is_active=True),
            Mock(id=uuid4(), integration_type="VISMA", is_active=True)
        ]
        
        integrations = integration_service.get_active_integrations(tenant_id)
        
        assert len(integrations) == 2
        assert all(integration.is_active for integration in integrations)
    
    @pytest.mark.asyncio
    async def test_fetch_invoices_for_tenant_no_integrations(self, integration_service):
        """Test fetching invoices when no integrations exist"""
        tenant_id = str(uuid4())

        with patch.object(integration_service, 'get_active_integrations', return_value=[]):
            result = await integration_service.fetch_invoices_for_tenant(tenant_id)

            assert result["integrations_processed"] == 0
            assert result["total_invoices"] == 0
            assert result["successful_integrations"] == 0


class TestIntegrationAPI:
    """Test integration API endpoints"""

    @pytest.fixture
    def client(self):
        from fastapi.testclient import TestClient
        from app.main import app
        return TestClient(app)

    @pytest.fixture
    def mock_current_user(self):
        return Mock(
            tenant_id=uuid4(),
            user_id=uuid4(),
            permissions=["MANAGE_INTEGRATIONS"]
        )

    def test_get_available_integrations(self, client):
        """Test getting available integration types"""
        response = client.get("/api/v1/integrations/available")

        assert response.status_code == 200
        data = response.json()

        assert len(data) == 3
        integration_types = [item["type"] for item in data]
        assert "FORTNOX" in integration_types
        assert "VISMA" in integration_types
        assert "HTTP" in integration_types

    def test_setup_fortnox_integration(self, client, mock_current_user):
        """Test setting up Fortnox integration"""
        integration_data = {
            "integration_type": "FORTNOX",
            "name": "Test Fortnox",
            "description": "Test integration",
            "configuration": {
                "client_id": "test-client-id",
                "client_secret": "test-client-secret"
            }
        }

        with patch('app.routers.integrations.get_current_tenant_user', return_value=mock_current_user):
            with patch('app.routers.integrations.check_permission'):
                with patch('app.database.get_db'):
                    response = client.post(
                        "/api/v1/integrations/setup",
                        json=integration_data
                    )

                    # Note: This would need proper database mocking for full test
                    # For now, we're testing the endpoint structure
                    assert response.status_code in [200, 201, 422]  # 422 for validation errors in test

    def test_setup_integration_missing_fields(self, client, mock_current_user):
        """Test setting up integration with missing required fields"""
        integration_data = {
            "integration_type": "FORTNOX",
            "configuration": {
                "client_id": "test-client-id"
                # Missing client_secret
            }
        }

        with patch('app.routers.integrations.get_current_tenant_user', return_value=mock_current_user):
            with patch('app.routers.integrations.check_permission'):
                response = client.post(
                    "/api/v1/integrations/setup",
                    json=integration_data
                )

                assert response.status_code == 400
                assert "Missing required field: client_secret" in response.json()["detail"]

    def test_setup_http_integration_invalid_url(self, client, mock_current_user):
        """Test setting up HTTP integration with invalid URL"""
        integration_data = {
            "integration_type": "HTTP",
            "configuration": {
                "base_url": "not-a-valid-url"
            }
        }

        with patch('app.routers.integrations.get_current_tenant_user', return_value=mock_current_user):
            with patch('app.routers.integrations.check_permission'):
                response = client.post(
                    "/api/v1/integrations/setup",
                    json=integration_data
                )

                assert response.status_code == 400
                assert "Invalid base_url format" in response.json()["detail"]


# Integration test fixtures and utilities
@pytest.fixture
def sample_fortnox_inbox_response():
    """Sample Fortnox inbox API response"""
    return {
        "Inbox": [
            {
                "Id": "123",
                "Name": "invoice-001.pdf",
                "Supplier": "Acme Corp",
                "Date": "2024-01-15",
                "Path": "/inbox/123",
                "Size": 12345,
                "Created": "2024-01-15T10:00:00Z"
            },
            {
                "Id": "124",
                "Name": "invoice-002.pdf",
                "Supplier": "Tech Solutions AB",
                "Date": "2024-01-16",
                "Path": "/inbox/124",
                "Size": 23456,
                "Created": "2024-01-16T11:00:00Z"
            }
        ]
    }


@pytest.fixture
def sample_visma_attachments_response():
    """Sample Visma attachments API response"""
    return {
        "Data": [
            {
                "Id": "att-001",
                "Name": "invoice-001.pdf",
                "SupplierName": "Acme Corp",
                "Created": "2024-01-15T10:00:00Z",
                "Modified": "2024-01-15T10:00:00Z",
                "Size": 12345
            }
        ]
    }


@pytest.fixture
def sample_visma_drafts_response():
    """Sample Visma supplier invoice drafts API response"""
    return {
        "Data": [
            {
                "Id": "draft-001",
                "InvoiceNumber": "INV-2024-001",
                "SupplierName": "Acme Corp",
                "InvoiceDate": "2024-01-15",
                "DueDate": "2024-02-15",
                "TotalAmount": 1500.00,
                "Currency": "SEK",
                "Created": "2024-01-15T10:00:00Z",
                "Modified": "2024-01-15T10:00:00Z"
            }
        ]
    }
