# Docker Rebuild Instructions - PyMuPDF Update

## Problem
Efter att ha lagt till PyMuPDF för PDF-bearbetning får du felmeddelandet:
```
ModuleNotFoundError: No module named 'fitz'
```

## Lösning
Docker-containrarna behöver byggas om för att inkludera PyMuPDF.

## Steg för att fixa

### 1. Stoppa alla containers
```bash
docker-compose down
```

### 2. Bygg om containers utan cache
```bash
# Bygg om alla services
docker-compose build --no-cache

# Eller bygg om specifika services
docker-compose build --no-cache backend-api
docker-compose build --no-cache scheduler  
docker-compose build --no-cache worker
```

### 3. Starta containers igen
```bash
docker-compose up -d
```

### 4. Verifiera installation
```bash
# Kontrollera att PyMuPDF är installerat
docker-compose exec backend-api python -c "import fitz; print('PyMuPDF version:', fitz.version)"
```

## Vad som ändrades

### requirements.txt
- ✅ Lade till `PyMuPDF>=1.26.0`
- ❌ Tog bort `pdf2image` (kommenterad ut)

### Dockerfile & Dockerfile.prod
- ❌ Tog bort `poppler-utils` (inte längre nödvändigt)
- ✅ Lade till kommentarer om PyMuPDF

### Fördelar
- Inga externa beroenden (poppler)
- Snabbare PDF-till-bild-konvertering
- Mindre Docker-images
- Mer robust PDF-hantering

## Troubleshooting

### Om du fortfarande får fel:
1. Kontrollera att requirements.txt innehåller `PyMuPDF>=1.26.0`
2. Bygg om med `--no-cache` flaggan
3. Kontrollera Docker logs: `docker-compose logs backend-api`

### Om bygget misslyckas:
1. Kontrollera internetanslutning
2. Rensa Docker cache: `docker system prune -a`
3. Försök igen med `docker-compose build --no-cache`
