# Aggie API Documentation

## Overview

Aggie is an AI-powered multi-tenant invoice management platform that provides automated invoice processing, accounting suggestions, and human-in-the-loop workflows.

## Base URL

- Development: `http://localhost:8000`
- Production: `https://api.yourcompany.com`

## Authentication

The API uses JWT (JSON Web Tokens) for authentication with optional TOTP-based two-factor authentication.

### Headers

All authenticated requests must include:

```
Authorization: Bearer <access_token>
X-Tenant-ID: <tenant_id>
```

## Authentication Endpoints

### POST /auth/token

Login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (No 2FA):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "requires_2fa": false,
  "message": "Login successful"
}
```

**Response (2FA Required):**
```json
{
  "temp_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "requires_2fa": true,
  "message": "2FA verification required"
}
```

### POST /auth/2fa/verify

Verify 2FA code and get full access token.

**Request:**
```json
{
  "code": "123456"
}
```

**Headers:**
```
Authorization: Bearer <temp_token>
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### POST /auth/2fa/setup

Setup 2FA for the current user.

**Response:**
```json
{
  "secret": "JBSWY3DPEHPK3PXP",
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "backup_codes": ["ABCD-1234", "EFGH-5678", ...]
}
```

### POST /auth/2fa/enable

Enable 2FA after setup verification.

**Request:**
```json
{
  "code": "123456"
}
```

### POST /auth/2fa/disable

Disable 2FA for the current user.

**Request:**
```json
{
  "password": "current_password",
  "code": "123456"
}
```

### GET /auth/me

Get current user information.

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "is_2fa_enabled": true,
  "tenants": [
    {
      "id": "uuid",
      "name": "Company ABC",
      "role": "admin",
      "permissions": ["invoices:read", "invoices:write", ...]
    }
  ]
}
```

## Invoice Endpoints

### POST /invoices/upload

Upload an invoice file for processing.

**Request:**
```
Content-Type: multipart/form-data

file: <PDF/PNG/JPG file>
supplier_name: "Optional supplier name"
```

**Response:**
```json
{
  "id": "uuid",
  "message": "Invoice uploaded successfully and processing started",
  "status": "pending"
}
```

### GET /invoices/

List invoices for the current tenant.

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records (default: 100)
- `status`: Filter by status (pending, processing, completed, failed, needs_review)

**Response:**
```json
[
  {
    "id": "uuid",
    "supplier_name": "Acme Corp",
    "invoice_number": "INV-001",
    "total_amount": 1500.00,
    "currency": "SEK",
    "status": "completed",
    "created_at": "2024-01-15T10:30:00Z",
    "original_filename": "invoice.pdf"
  }
]
```

### GET /invoices/{invoice_id}

Get detailed invoice information.

**Response:**
```json
{
  "id": "uuid",
  "supplier_name": "Acme Corp",
  "invoice_number": "INV-001",
  "invoice_date": "2024-01-15",
  "due_date": "2024-02-15",
  "total_amount": 1500.00,
  "currency": "SEK",
  "status": "completed",
  "extracted_text": "Full extracted text...",
  "extracted_context": "Structured context...",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:35:00Z",
  "original_filename": "invoice.pdf",
  "accounting_entries": [
    {
      "id": "uuid",
      "account_code": "4010",
      "account_name": "Office Supplies",
      "debit_amount": 1500.00,
      "credit_amount": null,
      "description": "Office supplies purchase",
      "confidence_score": 0.95,
      "is_validated": true
    }
  ]
}
```

### PUT /invoices/{invoice_id}/validate

Validate invoice accounting entries.

**Response:**
```json
{
  "message": "Invoice validated successfully"
}
```

### DELETE /invoices/{invoice_id}

Delete an invoice.

**Response:**
```json
{
  "message": "Invoice deleted successfully"
}
```

## Action Items Endpoints

### GET /action-items/

List action items for the current tenant.

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records (default: 100)
- `completed`: Filter by completion status (true/false)
- `priority`: Filter by priority (urgent, high, medium, low)
- `category`: Filter by category (review, validation, error, manual_entry)

**Response:**
```json
[
  {
    "id": "uuid",
    "title": "Review invoice from Acme Corp",
    "description": "AI processing completed with low confidence...",
    "priority": "medium",
    "category": "review",
    "is_completed": false,
    "created_at": "2024-01-15T10:30:00Z",
    "invoice_id": "uuid",
    "assigned_user": {
      "id": "uuid",
      "email": "<EMAIL>"
    }
  }
]
```

### GET /action-items/{action_item_id}

Get detailed action item information.

### PUT /action-items/{action_item_id}/complete

Mark action item as completed.

**Request:**
```json
{
  "resolution_notes": "Optional resolution notes"
}
```

### PUT /action-items/{action_item_id}/reopen

Reopen a completed action item.

### DELETE /action-items/{action_item_id}

Delete an action item.

## User Management Endpoints

### GET /users/

List users in the current tenant.

### GET /users/{user_id}

Get user details.

### PUT /users/{user_id}/role

Update user's role in the current tenant.

**Request:**
```json
{
  "role_id": "uuid"
}
```

### PUT /users/{user_id}/deactivate

Deactivate user in the current tenant.

### PUT /users/{user_id}/activate

Activate user in the current tenant.

### GET /users/roles/

List available roles.

**Response:**
```json
[
  {
    "id": "uuid",
    "name": "admin",
    "description": "Full administrative access",
    "permissions": ["invoices:read", "invoices:write", ...]
  }
]
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "detail": "Validation error message"
}
```

### 401 Unauthorized
```json
{
  "detail": "Could not validate credentials"
}
```

### 403 Forbidden
```json
{
  "detail": "Permission denied"
}
```

### 404 Not Found
```json
{
  "detail": "Resource not found"
}
```

### 422 Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "email"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

The API implements rate limiting:
- General API endpoints: 10 requests per second
- Authentication endpoints: 5 requests per second

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 9
X-RateLimit-Reset: **********
```

## Permissions

The following permissions are available:

- `invoices:read` - View invoices
- `invoices:write` - Create and edit invoices
- `invoices:delete` - Delete invoices
- `users:read` - View users
- `users:write` - Create and edit users
- `users:delete` - Delete users
- `accounting:read` - View accounting entries
- `accounting:write` - Create accounting entries
- `accounting:validate` - Validate accounting entries
- `settings:read` - View settings
- `settings:write` - Modify settings
- `reports:read` - View reports
- `reports:write` - Create reports
- `action_items:read` - View action items
- `action_items:write` - Create and edit action items
- `action_items:assign` - Assign action items
