from sqlalchemy import Column, String, Text, Float, <PERSON>olean, ForeignKey, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector
from .base import TenantBaseModel


class Invoice(TenantBaseModel):
    """Invoice model with tenant isolation"""
    __tablename__ = "invoices"

    # Import information
    import_typ = Column(String(50), nullable=False)  # "manuell", "Visma eEkonomi", "Fortnox"
    metadata_ERP = Column(JSON, nullable=True)  # Data objekt från ERP (tomt om manuellt)
    file_data = Column(Text, nullable=False)  # Faktisk data fil i base64 eller annat lämpligt format

    # Basic invoice information (extracted later)
    supplier_name = Column(String(255), nullable=True)
    invoice_number = Column(String(100), nullable=True)
    invoice_date = Column(String(50), nullable=True)
    due_date = Column(String(50), nullable=True)
    total_amount = Column(Float, nullable=True)
    currency = Column(String(10), nullable=True)
    raw_data = Column(JSON, nullable=True)  # Complete extracted JSON data from LLM

    # File information
    original_filename = Column(String(255), nullable=True)
    file_type = Column(String(50), nullable=True)  # pdf, png, jpg

    # Status tracking
    status = Column(String(50), nullable=False, default="pending")  # pending, processing, completed, failed, needs_review
    processing_error = Column(Text, nullable=True)

    # Relationships
    tenant = relationship("Tenant", back_populates="invoices")
    accounting_entries = relationship("AccountingEntry", back_populates="invoice", cascade="all, delete-orphan")
    invoice_vectors = relationship("InvoiceVector", back_populates="invoice", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="invoice", passive_deletes=True)
    session = relationship("Session", back_populates="invoice", uselist=False, cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Invoice(id={self.id}, import_typ='{self.import_typ}', status='{self.status}')>"


class AccountingEntry(TenantBaseModel):
    """Accounting entry suggestions from AI"""
    __tablename__ = "accounting_entries"

    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False)
    
    # Accounting data
    account_code = Column(String(50), nullable=False)
    account_name = Column(String(255), nullable=False)
    debit_amount = Column(Float, nullable=True)
    credit_amount = Column(Float, nullable=True)
    description = Column(Text, nullable=True)
    
    # AI confidence and validation
    confidence_score = Column(Float, nullable=False, default=0.0)  # 0.0 to 1.0
    is_validated = Column(Boolean, default=False, nullable=False)
    validated_by = Column(UUID(as_uuid=True), nullable=True)  # User ID who validated
    
    # Additional metadata
    entry_data = Column(JSON, nullable=True)  # Additional structured data
    
    # Relationships
    invoice = relationship("Invoice", back_populates="accounting_entries")

    def __repr__(self):
        return f"<AccountingEntry(id={self.id}, account='{self.account_code}', confidence={self.confidence_score})>"


class InvoiceVector(TenantBaseModel):
    """Vector embeddings for RAG"""
    __tablename__ = "invoice_vectors"

    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False)
    embedding = Column(Vector(1536), nullable=False)  # OpenAI embedding dimension
    content_hash = Column(String(64), nullable=False)  # Hash of the content used for embedding
    
    # Relationships
    invoice = relationship("Invoice", back_populates="invoice_vectors")

    def __repr__(self):
        return f"<InvoiceVector(id={self.id}, invoice_id={self.invoice_id})>"


class Session(TenantBaseModel):
    """Session model - 1:1 relation with Invoice"""
    __tablename__ = "sessions"

    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False, unique=True)

    # Session status
    status = Column(String(50), nullable=False, default="pending")  # pending, processing, completed, failed, action-required
    current_step = Column(String(50), nullable=True)  # Current processing step

    # Processing results
    extracted_data = Column(Text, nullable=True)  # Result from "Extrahera" step
    extracted_reasoning = Column(Text, nullable=True)  # Reasoning from "Extrahera" step

    context_data = Column(Text, nullable=True)  # Result from "Kontext" step
    context_reasoning = Column(Text, nullable=True)  # Reasoning from "Kontext" step

    account_data = Column(JSON, nullable=True)  # Result from "Hitta konto" step (JSON array)
    account_reasoning = Column(Text, nullable=True)  # Reasoning from "Hitta konto" step

    booking_result = Column(JSON, nullable=True)  # Result from "Bokföra" step
    booking_reasoning = Column(Text, nullable=True)  # Reasoning from "Bokföra" step

    # Error tracking
    error_message = Column(Text, nullable=True)
    failed_step = Column(String(50), nullable=True)

    # Token usage summary
    total_input_tokens = Column(Integer, nullable=True, default=0)
    total_output_tokens = Column(Integer, nullable=True, default=0)

    # Relationships
    tenant = relationship("Tenant", back_populates="sessions", overlaps="sessions")
    invoice = relationship("Invoice", back_populates="session")
    session_logs = relationship("SessionLog", back_populates="session", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="session", passive_deletes=True)

    def __repr__(self):
        return f"<Session(id={self.id}, invoice_id={self.invoice_id}, status='{self.status}')>"


class SessionLog(TenantBaseModel):
    """Session log model - logs each step in the processing flow"""
    __tablename__ = "session_logs"

    session_id = Column(UUID(as_uuid=True), ForeignKey("sessions.id"), nullable=False)

    # Log information
    step_name = Column(String(50), nullable=False)  # "extrahera", "kontext", "hitta_konto", "bokfora"
    prompt_sent = Column(Text, nullable=False)  # Prompt skickad till LLM
    llm_response = Column(Text, nullable=False)  # Svar från LLM
    reasoning = Column(Text, nullable=True)  # Reasoning från LLM

    # Metadata
    execution_time_ms = Column(Float, nullable=True)
    success = Column(Boolean, nullable=False, default=True)
    error_message = Column(Text, nullable=True)

    # Token usage
    input_tokens = Column(Integer, nullable=True)
    output_tokens = Column(Integer, nullable=True)

    # Relationships
    tenant = relationship("Tenant", back_populates="session_logs", overlaps="session_logs")
    session = relationship("Session", back_populates="session_logs")

    def __repr__(self):
        return f"<SessionLog(id={self.id}, session_id={self.session_id}, step='{self.step_name}')>"
