# File Processing Implementation för Invoice Processing

## Översikt

Denna implementation hanterar olika filtyper (PDF och bilder) i invoice processing systemet med gpt-4o-mini modellen.

## Arkitektur

### Filflöde

```
Invoice Upload (base64) 
    ↓
OCRService.process_file_for_llm()
    ↓
PDF? → Extract text → LLM (text only)
Image? → Pass base64 → LLM (vision API)
    ↓
LLMProvider.send_prompt() 
    ↓
OpenAI gpt-4o-mini (multimodal)
```

## Komponenter

### 1. OCRService (`app/services/ocr_service.py`)

**Huvudmetod:** `process_file_for_llm(file_data_base64: str, file_type: str) -> Tuple[Optional[str], Optional[str]]`

**Funktionalitet:**
- **PDF-filer**: Extraherar text med `pypdf` biblioteket
- **Bildfiler** (JPG, PNG): Returnerar base64 data direkt
- **Returvärde**: `(text_content, image_data)` - en av dem är alltid `None`

**PDF Text Extraktion:**
1. Försöker extrahera text direkt från PDF med `pypdf`
2. Om det misslyckas eller ger minimal text, försöker external OCR (kräver poppler)
3. Fallback: Returnerar vad som kunde extraheras eller felmeddelande

### 2. LLMProvider (`app/services/llm_provider.py`)

**Uppdaterad metod:** `send_prompt(prompt, system_prompt, image_data=None, file_type=None)`

**Multimodal Support:**
- **Text-only**: Skickar vanlig text prompt
- **Med bild**: Skapar OpenAI Vision API format med base64 bild

**OpenAI Vision Format:**
```json
{
  "role": "user",
  "content": [
    {"type": "text", "text": "prompt text"},
    {
      "type": "image_url", 
      "image_url": {
        "url": "data:image/jpeg;base64,{base64_data}",
        "detail": "high"
      }
    }
  ]
}
```

### 3. InvoiceProcessingService (`app/services/invoice_processing_service.py`)

**Uppdaterat `_step_extrahera` steg:**

```python
# Bearbeta fil baserat på typ
text_content, image_data = self.ocr_service.process_file_for_llm(
    invoice.file_data, 
    invoice.file_type
)

if text_content:
    # PDF med extraherad text
    response = await self.llm_service.send_prompt(user_prompt, system_prompt)
else:
    # Bild - skicka som base64
    response = await self.llm_service.send_prompt(
        user_prompt, system_prompt, 
        image_data=image_data, file_type=invoice.file_type
    )
```

## Konfiguration

### Miljövariabler (.env)
```
LLM_PROVIDER=openai
OPENAI_API_KEY=sk-...
OPENAI_MODEL=gpt-4o-mini
```

### Modell
- **gpt-4o-mini**: Stöder både text och vision (bilder)
- **Vision capabilities**: Kan läsa text från bilder (OCR)
- **Cost-effective**: Billigare än gpt-4o men med bra prestanda

## Filtyper som stöds

### PDF-filer
- **Text-baserade PDFs**: Extraherar text direkt med pypdf
- **Bild-baserade PDFs**: Kräver external OCR (poppler + pdf2image)
- **Fallback**: Skickar felmeddelande om OCR inte är tillgängligt

### Bildfiler
- **JPG/JPEG**: Skickas direkt till gpt-4o-mini vision
- **PNG**: Skickas direkt till gpt-4o-mini vision
- **Format**: Base64 encoded i OpenAI Vision API format

## Testning

### Test Scripts
- `test_file_processing.py`: Grundläggande tester för OCR och LLM
- `test_pdf_processing.py`: Specifik PDF text extraktion test

### Kör tester
```bash
cd apps/backend-api
python test_file_processing.py
python test_pdf_processing.py
```

## Begränsningar och förbättringar

### Nuvarande begränsningar
1. **Poppler dependency**: External OCR för bild-PDFs kräver poppler installation
2. **Ingen batch processing**: Hanterar en fil åt gången
3. **Begränsad felhantering**: Grundläggande error handling

### Framtida förbättringar
1. **Implementera external OCR**: Använd gpt-4o-mini för bild-baserade PDFs
2. **Batch processing**: Hantera flera sidor/filer samtidigt
3. **Caching**: Cache extraherad text för återanvändning
4. **Bättre felhantering**: Mer detaljerad error reporting

## Säkerhet

### Data hantering
- **Base64 encoding**: Säker överföring av fildata
- **Temporary files**: Rensas automatiskt efter PDF bearbetning
- **No file upload to OpenAI**: Filer skickas som base64 i request, inte uppladdade

### API säkerhet
- **API keys**: Hanteras via miljövariabler
- **Rate limiting**: Hanteras av OpenAI API
- **Data retention**: Ingen data lagras hos OpenAI (enligt deras policy)

## Prestanda

### Optimeringar
- **Lazy loading**: OCR service initialiseras endast när behövs
- **Efficient text extraction**: Använder pypdf för snabb PDF text extraktion
- **High detail vision**: Använder "high" detail för bättre OCR från bilder

### Monitoring
- **Logging**: Detaljerad loggning av alla steg
- **Error tracking**: Spårar fel i SessionLog
- **Performance metrics**: Execution time tracking i processing steps
