# Multi-stage build for backend-api
# Base stage with common dependencies
FROM python:3.11-slim AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*
    # tesseract-ocr removed - using external OCR API/multimodal LLM instead
    # poppler-utils removed - using PyMuPDF instead (no external dependencies)

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Upgrade pip and install wheel for better compatibility
RUN pip install --upgrade pip setuptools wheel

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Development stage
FROM base AS development

# Install development tools
RUN apt-get update && apt-get install -y \
    curl \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Install development Python packages
RUN pip install --no-cache-dir \
    ipdb \
    pytest-watch \
    black \
    isort \
    flake8

# Copy application code
COPY . .

# Create uploads directory and celery directories
RUN mkdir -p uploads celery

# Set development environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Create non-root user for security but allow root for development flexibility
RUN addgroup --system --gid 1001 appgroup && \
    adduser --system --uid 1001 --gid 1001 --no-create-home appuser && \
    chown -R appuser:appgroup /app

# Default to appuser but can be overridden in docker-compose
USER appuser

EXPOSE 8000

# Development command with hot reload and memory-friendly file watching
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "/app", "--reload-exclude", "*.pyc", "--reload-exclude", "__pycache__/*", "--reload-exclude", ".git/*", "--reload-exclude", "node_modules/*", "--reload-exclude", "*.log"]

# Production stage
FROM base AS production

# Copy application code
COPY . .

# Create uploads directory and celery directories
RUN mkdir -p uploads celery

# Create non-root user for security
RUN addgroup --system --gid 1001 appgroup && \
    adduser --system --uid 1001 --gid 1001 --no-create-home appuser && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

EXPOSE 8000

# Production command
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

# Default to development stage
FROM development
