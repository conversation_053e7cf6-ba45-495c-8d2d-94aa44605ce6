# Invoice Integration System

The Aggie platform supports multiple invoice integration types to automatically fetch invoices from different accounting systems and sources.

## Supported Integrations

### 1. Fortnox Integration

Fortnox is a popular Swedish accounting software. The integration uses OAuth2 authentication and fetches invoices from the Fortnox Inbox API.

**Features:**
- OAuth2 authentication
- Automatic invoice fetching from Inbox
- File download support
- Post-processing callbacks

**Setup:**
1. Register your application in Fortnox Developer Portal
2. Get Client ID and Client Secret
3. Configure in Aggie admin panel
4. Complete OAuth2 flow

**API Endpoints Used:**
- `/oauth/authorize` - OAuth2 authorization
- `/oauth/token` - Token exchange and refresh
- `/inbox` - Fetch inbox items
- `/inbox/{id}` - Get inbox item details
- `/inbox/{id}/file` - Download invoice file

### 2. Visma eEkonomi Integration

Visma eEkonomi is another popular Nordic accounting software. The integration supports both attachments and supplier invoice drafts.

**Features:**
- OAuth2 authentication
- Fetch from attachments endpoint
- Fetch from supplier invoice drafts
- Dual data source support

**Setup:**
1. Register your application in Visma Developer Portal
2. Get Client ID and Client Secret
3. Configure in Aggie admin panel
4. Complete OAuth2 flow

**API Endpoints Used:**
- `/oauth/authorize` - OAuth2 authorization
- `/oauth/token` - Token exchange and refresh
- `/attachments` - Fetch attachments
- `/supplierinvoicedrafts` - Fetch invoice drafts
- `/companies/current` - Connection testing

### 3. Simple HTTP Integration

For systems that expose invoices via simple HTTP/HTTPS endpoints without OAuth2.

**Features:**
- Multiple authentication methods (none, basic, bearer, API key)
- URL validation and security checks
- Support for JSON, HTML, and direct file responses
- File type validation
- Private network protection

**Setup:**
1. Configure base URL
2. Set authentication method
3. Configure allowed domains (optional)
4. Set file size and type limits

**Supported Response Types:**
- JSON with invoice metadata
- HTML with file links
- Direct file downloads (PDF, PNG, JPG)

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Fortnox
FORTNOX_CLIENT_ID=your-fortnox-client-id
FORTNOX_CLIENT_SECRET=your-fortnox-client-secret
FORTNOX_API_URL=https://api.fortnox.se/3/

# Visma eEkonomi
VISMA_CLIENT_ID=your-visma-client-id
VISMA_CLIENT_SECRET=your-visma-client-secret
VISMA_API_URL=https://eaccountingapi.vismaonline.com/v2/
```

### Database Setup

Run the migration to create integration tables:

```bash
cd apps/backend-api
alembic upgrade head
```

## API Usage

### List Available Integrations

```http
GET /api/v1/integrations/available
```

### Setup New Integration

```http
POST /api/v1/integrations/setup
Content-Type: application/json

{
  "integration_type": "FORTNOX",
  "name": "Main Fortnox Account",
  "description": "Primary accounting system",
  "configuration": {
    "client_id": "your-client-id",
    "client_secret": "your-client-secret"
  }
}
```

### OAuth2 Flow

1. **Initiate OAuth2 Login:**
```http
POST /api/v1/integrations/oauth/fortnox/login
Content-Type: application/json

{
  "integration_id": "uuid-of-integration",
  "redirect_uri": "https://yourapp.com/oauth/callback"
}
```

2. **Handle OAuth2 Callback:**
```http
POST /api/v1/integrations/oauth/fortnox/callback
Content-Type: application/json

{
  "code": "authorization-code",
  "state": "state-parameter",
  "integration_id": "uuid-of-integration"
}
```

### Test Connection

```http
POST /api/v1/integrations/{integration_id}/test-connection
```

## Security Considerations

### Data Encryption

- **Strong Fernet Encryption**: All sensitive data encrypted using cryptography.fernet.Fernet
- **OAuth2 Tokens**: Access tokens, refresh tokens, and extra data fully encrypted at rest
- **Configuration Data**: Client secrets, API keys, and all integration configuration encrypted
- **Key Derivation**: PBKDF2 with SHA-256 and 100,000 iterations for secure key generation
- **Automatic Encryption/Decryption**: Transparent encryption in database layer using custom SQLAlchemy TypeDecorator

### Access Control

- Row-level security (RLS) ensures tenant isolation
- OAuth2 tokens are scoped to specific integrations
- API endpoints require proper authentication and permissions

### Network Security

- HTTP integration validates URLs to prevent SSRF attacks
- Private network access is blocked by default
- File size and type restrictions prevent abuse
- Rate limiting protects against API abuse

## Monitoring and Logging

### Integration Status

Each integration tracks:
- Last sync time
- Sync status (success/failed/partial)
- Error messages
- Connection health

### Logging

The system logs:
- Integration sync attempts
- OAuth2 token refresh events
- Connection test results
- Error conditions and failures

## Troubleshooting

### Common Issues

1. **OAuth2 Token Expired**
   - Tokens are automatically refreshed
   - Check integration status for refresh failures
   - Re-authorize if refresh token is invalid

2. **Connection Failures**
   - Use test connection endpoint to diagnose
   - Check network connectivity
   - Verify API credentials

3. **No Invoices Fetched**
   - Check integration sync status
   - Verify date filters
   - Review API rate limits

### Debug Mode

Enable debug logging by setting log level to DEBUG in your environment.

## Encryption Implementation

### Custom Fernet Encryption

The system uses a custom SQLAlchemy TypeDecorator for transparent encryption:

```python
class Encrypted(TypeDecorator):
    """Custom SQLAlchemy type for encrypted fields using Fernet encryption"""
    impl = Text
    cache_ok = True

    def process_bind_param(self, value, dialect):
        """Encrypt value before storing in database"""
        if value is not None:
            pickled_value = pickle.dumps(value)
            encrypted_value = self.fernet.encrypt(pickled_value)
            return encrypted_value.decode('utf-8')
        return value

    def process_result_value(self, value, dialect):
        """Decrypt value after retrieving from database"""
        if value is not None:
            encrypted_bytes = value.encode('utf-8')
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return pickle.loads(decrypted_bytes)
        return value
```

### Key Features

- **Transparent**: Encryption/decryption happens automatically
- **Type Agnostic**: Supports any Python data type via pickle
- **Secure**: Uses Fernet (AES 128 in CBC mode with HMAC-SHA256)
- **Key Derivation**: PBKDF2 with 100,000 iterations
- **Production Ready**: Battle-tested cryptography library

### Encrypted Fields

- `InvoiceIntegration.configuration` - All integration settings
- `OAuth2Token.access_token` - OAuth access tokens
- `OAuth2Token.refresh_token` - OAuth refresh tokens
- `OAuth2Token.extra_data` - Additional token metadata

## Development

### Adding New Integrations

1. Create new fetcher class inheriting from `InvoiceFetcher`
2. Implement required abstract methods
3. Add integration type to schemas
4. Update API endpoints
5. Add tests

### Testing

Run integration tests:

```bash
cd apps/backend-api
pytest tests/test_integrations.py -v
```

## Quick Start Guide

### 1. Setup Fortnox Integration

1. **Register Application:**
   - Go to [Fortnox Developer Portal](https://developer.fortnox.se/)
   - Create new application
   - Set redirect URI to: `http://localhost:3000/integrations/oauth/fortnox/callback`
   - Note down Client ID and Client Secret

2. **Configure Environment:**
   ```bash
   FORTNOX_CLIENT_ID=your-client-id
   FORTNOX_CLIENT_SECRET=your-client-secret
   ```

3. **Setup in Aggie:**
   - Login to admin panel
   - Go to Integrations
   - Click "Add Integration"
   - Select "Fortnox"
   - Enter Client ID and Secret
   - Complete OAuth2 flow

### 2. Setup Visma eEkonomi Integration

1. **Register Application:**
   - Go to [Visma Developer Portal](https://developer.vismaonline.com/)
   - Create new application
   - Set redirect URI to: `http://localhost:3000/integrations/oauth/visma/callback`
   - Note down Client ID and Client Secret

2. **Configure Environment:**
   ```bash
   VISMA_CLIENT_ID=your-client-id
   VISMA_CLIENT_SECRET=your-client-secret
   ```

3. **Setup in Aggie:**
   - Login to admin panel
   - Go to Integrations
   - Click "Add Integration"
   - Select "Visma eEkonomi"
   - Enter Client ID and Secret
   - Complete OAuth2 flow

### 3. Setup HTTP Integration

1. **Configure in Aggie:**
   - Login to admin panel
   - Go to Integrations
   - Click "Add Integration"
   - Select "HTTP/HTTPS"
   - Enter base URL
   - Configure authentication if needed
   - Test connection

## Support

For integration-specific issues:
- Fortnox: Check Fortnox Developer Documentation
- Visma: Check Visma Developer Portal
- HTTP: Verify endpoint accessibility and response format
