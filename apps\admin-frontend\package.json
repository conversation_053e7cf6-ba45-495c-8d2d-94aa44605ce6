{"name": "admin-frontend", "version": "1.0.0", "private": true, "dependencies": {"@aggie/ui-components": "file:../../packages/ui-components", "@aggie/shared-types": "file:../../packages/shared-types", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "^5.0.1", "typescript": "^5.5.0", "axios": "^1.6.0", "react-hook-form": "^7.48.0", "react-query": "^3.39.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "qrcode.react": "^3.1.0", "react-hot-toast": "^2.4.0", "@testing-library/jest-dom": "^5.16.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.0", "@tailwindcss/forms": "^0.5.0"}, "scripts": {"start": "PORT=3001 react-scripts start", "start:windows": "set PORT=3001 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/qrcode.react": "^1.0.0"}}