#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a demo user for testing the application.
This creates the user shown in the demo credentials on the login page.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models.user import User, Role, TenantUser
from app.models.tenant import Tenant
from app.utils.security import get_password_hash
import uuid


def create_demo_user():
    """Create demo user with credentials: <EMAIL> / password123"""
    db = SessionLocal()
    
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("Demo user already exists!")
            return
        
        # Check if we have a tenant
        tenant = db.query(Tenant).first()
        if not tenant:
            print("Creating default tenant...")
            tenant = Tenant(
                name="Demo Tenant",
                domain="demo.example.com",
                is_active=True
            )
            db.add(tenant)
            db.commit()
            db.refresh(tenant)
            print(f"Created tenant: {tenant.name}")
        
        # Check if we have admin role
        admin_role = db.query(Role).filter(Role.name == "admin").first()
        if not admin_role:
            print("Creating admin role...")
            admin_role = Role(
                name="admin",
                description="Administrator",
                permissions=[
                    "invoices:read", "invoices:write", "invoices:delete",
                    "users:read", "users:write", "users:delete",
                    "accounting:read", "accounting:write", "accounting:validate",
                    "settings:read", "settings:write",
                    "reports:read", "reports:write",
                    "action_items:read", "action_items:write", "action_items:assign"
                ]
            )
            db.add(admin_role)
            db.commit()
            db.refresh(admin_role)
            print(f"Created role: {admin_role.name}")
        
        # Create demo user
        print("Creating demo user...")
        demo_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            is_active=True,
            is_superuser=True,
            is_2fa_enabled=False
        )
        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)
        print(f"Created user: {demo_user.email}")
        
        # Link user to tenant with admin role
        print("Linking user to tenant...")
        tenant_user = TenantUser(
            user_id=demo_user.id,
            tenant_id=tenant.id,
            role_id=admin_role.id,
            is_active=True
        )
        db.add(tenant_user)
        db.commit()
        print("User linked to tenant with admin role")
        
        print("\n✅ Demo user created successfully!")
        print("Email: <EMAIL>")
        print("Password: password123")
        print("\nYou can now log in to the application.")
        
    except Exception as e:
        print(f"❌ Error creating demo user: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    create_demo_user()
