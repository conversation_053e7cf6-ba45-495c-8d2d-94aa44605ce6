{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "types": ["node"], "target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "files": [], "include": [], "references": [{"path": "./tsconfig.spec.json"}]}