# Aggie Nx Monorepo

Detta projekt har refaktorerats från en enkel multi-directory setup till en robust Nx monorepo-struktur.

## 📁 Projektstruktur

```
aggie/
├── apps/
│   ├── backend-api/          # FastAPI backend
│   ├── admin-frontend/       # React admin konsol
│   └── app-frontend/         # React användarapp
├── packages/
│   ├── ui-components/        # Delade React komponenter
│   └── shared-types/         # Delade TypeScript typer
├── nx.json                   # Nx workspace konfiguration
├── package.json              # Root package.json med workspace setup
├── tsconfig.base.json        # Bas TypeScript konfiguration
└── docker-compose.yml        # Fullständig setup med reverse proxy
```

## 🚀 Kom igång

### Utveckling med Docker (Rekommenderat)

```bash
docker-compose up --build
```

### Lokal utveckling med Nx

1. **Installera dependencies**:
   ```bash
   npm install --legacy-peer-deps
   ```

2. **Bygg delade paket**:
   ```bash
   nx build shared-types
   nx build ui-components
   ```

3. **Starta applikationer**:
   ```bash
   # Admin frontend (port 3001)
   nx serve admin-frontend
   
   # User app frontend (port 3002)  
   nx serve app-frontend
   
   # Backend API (port 8000)
   nx serve backend-api
   ```

## 🌐 Åtkomst

- **Admin Console**: http://console.aggi.se:3001 eller http://localhost:3001
- **User App**: http://app.aggi.se:3002 eller http://localhost:3002
- **Backend API**: http://localhost:8000

## 📦 Delade Paket

### @aggie/shared-types
Innehåller alla delade TypeScript interfaces och typer:
- `User`, `TenantInfo`, `LoginResponse`
- `Invoice`, `ActionItem`, `AccountingEntry`
- `TwoFASetupResponse`, `ApiError`

### @aggie/ui-components
Innehåller delade React komponenter och utilities:
- `classNames` utility funktion
- Framtida delade UI komponenter

## 🔧 Användning av Delade Paket

```typescript
// Importera typer
import { User, Invoice } from '@aggie/shared-types';

// Importera UI utilities
import { classNames } from '@aggie/ui-components';
```

## 📝 Nx Kommandon

```bash
# Visa alla projekt
nx show projects

# Bygg specifikt projekt
nx build admin-frontend

# Testa specifikt projekt
nx test shared-types

# Linta alla projekt
nx lint

# Visa dependency graph
nx dep-graph
```

## 🐳 Docker Kommandon

```bash
# Starta alla services
docker-compose up

# Starta i bakgrunden
docker-compose up -d

# Stoppa alla services
docker-compose down

# Visa status
docker-compose ps

# Visa logs
docker-compose logs -f
```

## 🔄 Nästa Steg

1. **Differentiera frontend-apparna** genom att anpassa `apps/admin-frontend/` och `apps/app-frontend/` för deras specifika användningsfall

2. **Lägg till fler delade komponenter** i `packages/ui-components/` när du identifierar gemensamma UI-element

3. **Konfigurera CI/CD** för att bygga och deploya från monorepo-strukturen

4. **Lägg till end-to-end tester** som testar integrationen mellan frontend och backend

## 🛠️ Felsökning

### Docker Issues
- Om containrar inte startar, kör: `docker-compose down && docker-compose up --build`
- Kontrollera logs: `docker-compose logs [service-name]`

### Nx Issues
- Om shared packages inte hittas, kör: `npm install --legacy-peer-deps`
- Rensa Nx cache: `nx reset`

### Port Konflikter
- Admin frontend: Port 3001
- App frontend: Port 3002
- Backend API: Port 8000
- PostgreSQL: Port 5432
- Redis: Port 6379
