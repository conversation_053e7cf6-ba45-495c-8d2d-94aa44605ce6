"""Remove agent_session_id from action_items

Revision ID: 016
Revises: 015
Create Date: 2025-08-07 09:25:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '016'
down_revision = '015'
branch_labels = None
depends_on = None


def upgrade():
    # Drop foreign key constraint first (if it exists)
    try:
        op.drop_constraint('action_items_agent_session_id_fkey', 'action_items', type_='foreignkey')
    except:
        # Constraint might not exist, continue
        pass
    
    # Drop the agent_session_id column
    try:
        op.drop_column('action_items', 'agent_session_id')
    except:
        # Column might not exist, continue
        pass


def downgrade():
    # Add agent_session_id column back
    op.add_column('action_items', sa.Column('agent_session_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # Note: We don't recreate the foreign key constraint since agent_sessions table doesn't exist
