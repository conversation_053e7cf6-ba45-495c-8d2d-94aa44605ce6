"""
Example of how to use the custom email validator with Pydantic models.
This shows how to integrate our custom email validator into your existing schemas.
"""

from pydantic import BaseModel, field_validator
from typing import Optional
import sys
import os

# Add the app directory to Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.email_validator import pydantic_email_validator


class UserCreate(BaseModel):
    """Example user creation schema with custom email validation."""
    
    name: str
    email: str
    password: str
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email using our custom validator."""
        return pydantic_email_validator(v)


class UserUpdate(BaseModel):
    """Example user update schema with optional email validation."""
    
    name: Optional[str] = None
    email: Optional[str] = None
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v: Optional[str]) -> Optional[str]:
        """Validate email using our custom validator."""
        if v is None:
            return v
        return pydantic_email_validator(v)


class UserResponse(BaseModel):
    """Example user response schema."""
    
    id: int
    name: str
    email: str
    is_active: bool = True
    
    class Config:
        from_attributes = True  # For SQLAlchemy models


# Example usage:
if __name__ == "__main__":
    # Test valid email
    try:
        user = UserCreate(
            name="John Doe",
            email="<EMAIL>",
            password="secure_password"
        )
        print(f"✅ Valid user: {user.email}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test invalid email
    try:
        user = UserCreate(
            name="Jane Doe",
            email="invalid-email",
            password="secure_password"
        )
        print(f"✅ Valid user: {user.email}")
    except Exception as e:
        print(f"❌ Error: {e}")
