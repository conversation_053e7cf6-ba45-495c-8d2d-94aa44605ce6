"""Tenant management endpoints"""
from fastapi import APIRouter, HTTPException, status
from sqlalchemy.orm import Session

from app.dependencies.auth import CurrentTenantUser, TenantScopedDB
from app.models.tenant import Tenant
from app.schemas.tenant import TenantResponse, TenantUpdate
from app.utils.permissions import Permission, check_permission
from app.routers.base import handle_database_error

router = APIRouter()


@router.get("/", response_model=TenantResponse)
async def get_current_tenant(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Get current tenant information"""
    # Any authenticated user can read their tenant info
    tenant = db.query(Tenant).filter(Tenant.id == tenant_user.tenant_id).first()
    
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )
    
    return tenant


@router.put("/", response_model=TenantResponse)
async def update_current_tenant(
    update_data: TenantUpdate,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Update current tenant information"""
    # Check permissions - only users with settings write permission can update tenant settings
    if not check_permission(tenant_user.role.permissions, Permission.SETTINGS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Settings write permission required to update tenant settings"
        )
    
    tenant = db.query(Tenant).filter(Tenant.id == tenant_user.tenant_id).first()
    
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )
    
    # Update fields that were provided
    update_dict = update_data.model_dump(exclude_unset=True)
    
    for field, value in update_dict.items():
        if hasattr(tenant, field):
            setattr(tenant, field, value)
    
    try:
        db.commit()
        db.refresh(tenant)
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return tenant
