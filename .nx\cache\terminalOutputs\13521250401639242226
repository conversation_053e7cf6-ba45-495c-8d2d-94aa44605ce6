


[1m[33mwarn[39m[22m - The `content` option in your Tailwind CSS configuration is missing or empty.
[1m[33mwarn[39m[22m - Configure your content sources or your generated CSS will be missing styles.
[1m[33mwarn[39m[22m - https://tailwindcss.com/docs/content-configuration
Source path: D:\Visual Code Repo\aggie\apps\app-frontend\src\index.css
Setting up new context...
Finding changed files: 0.248ms
Reading changed files: 0.006ms
Sorting candidates: 0.004ms
Generate rules: 1.767ms
Build stylesheet: 0.623ms
Potential classes:  [33m1[39m
Active contexts:  [33m1[39m
JIT TOTAL: 82.701ms




Source path: D:\Visual Code Repo\aggie\apps\app-frontend\src\appCustomStyles.css
JIT TOTAL: 96.118ms


Entrypoint [1mmain[39m[22m [1m[33m[big][39m[22m 446 KiB = [1m[32mruntime.eaefff53f980ba55.js[39m[22m 1.81 KiB [1m[32mmain.fd1fb24eb1df2211.css[39m[22m 20 KiB [1m[32mmain.5d76e80efa55a2c5.js[39m[22m 424 KiB
Entrypoint [1mpolyfills[39m[22m 1.93 KiB = [1m[32mruntime.eaefff53f980ba55.js[39m[22m 1.81 KiB [1m[32mpolyfills.e17f9822b6dcff24.js[39m[22m 119 bytes
Entrypoint [1mstyles[39m[22m 6.76 KiB = [1m[32mruntime.eaefff53f980ba55.js[39m[22m 1.81 KiB [1m[32mstyles.9a7e9e1fdb10db38.css[39m[22m 4.82 KiB [1m[32mstyles.d273e0566dd915ff.js[39m[22m 132 bytes
chunk (runtime: runtime) [1m[32mruntime.eaefff53f980ba55.js[39m[22m (runtime) 4.64 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mpolyfills.e17f9822b6dcff24.js[39m[22m (polyfills) 3.55 KiB [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mmain.fd1fb24eb1df2211.css[39m[22m, [1m[32mmain.5d76e80efa55a2c5.js[39m[22m (main) 1 MiB (javascript) 29.7 KiB (css/mini-extract) [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mstyles.9a7e9e1fdb10db38.css[39m[22m, [1m[32mstyles.d273e0566dd915ff.js[39m[22m (styles) 50 bytes (javascript) 10.3 KiB (css/mini-extract) [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (72f9f7c8dfd8a462)
