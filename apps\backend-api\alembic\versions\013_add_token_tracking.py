"""Add token tracking fields to sessions and session_logs

Revision ID: 013_add_token_tracking
Revises: 012_enable_rls_for_sessions
Create Date: 2025-01-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '013'
down_revision = '012'
branch_labels = None
depends_on = None


def upgrade():
    """Add token tracking fields to sessions and session_logs tables"""
    
    # Add token fields to session_logs table
    op.add_column('session_logs', sa.Column('input_tokens', sa.Integer(), nullable=True))
    op.add_column('session_logs', sa.Column('output_tokens', sa.Integer(), nullable=True))
    
    # Add token summary fields to sessions table
    op.add_column('sessions', sa.Column('total_input_tokens', sa.Integer(), nullable=True, default=0))
    op.add_column('sessions', sa.Column('total_output_tokens', sa.Integer(), nullable=True, default=0))


def downgrade():
    """Remove token tracking fields"""
    
    # Remove token fields from sessions table
    op.drop_column('sessions', 'total_output_tokens')
    op.drop_column('sessions', 'total_input_tokens')
    
    # Remove token fields from session_logs table
    op.drop_column('session_logs', 'output_tokens')
    op.drop_column('session_logs', 'input_tokens')
