from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import os
import aiofiles
import logging

from app.dependencies.auth import CurrentTenantUser, TenantScopedDB
from app.models.invoice import Invoice, AccountingEntry
from app.models.action_item import ActionItem
from app.models.user import TenantUser
from app.utils.permissions import Permission, check_permission
from app.utils.security import generate_secure_filename
from app.config import settings
from app.routers.base import handle_database_error

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/upload")
async def upload_invoice(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    file: UploadFile = File(...),
    supplier_name: Optional[str] = Form(None)
):
    """Upload and process an invoice file"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Validate file type
    allowed_types = ["application/pdf", "image/png", "image/jpeg", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not supported. Please upload PDF, PNG, or JPG files."
        )
    
    # Validate file size
    if file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {settings.max_file_size / (1024*1024):.1f}MB"
        )
    
    # Generate secure filename
    secure_filename = generate_secure_filename(file.filename)
    file_path = os.path.join(settings.upload_dir, str(tenant_user.tenant_id), secure_filename)
    
    # Ensure upload directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # Read file content and encode as base64
    try:
        content = await file.read()
        import base64
        file_data_base64 = base64.b64encode(content).decode('utf-8')
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to read file"
        )

    # Create invoice record with new system
    invoice = Invoice(
        tenant_id=tenant_user.tenant_id,
        import_typ="manuell",
        file_data=file_data_base64,
        metadata_ERP=None,
        supplier_name=supplier_name or "Unknown",
        original_filename=file.filename,
        file_type=file.content_type.split('/')[-1],
        status="pending"
    )
    
    try:
        db.add(invoice)
        db.commit()
        db.refresh(invoice)
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    # Create session for new processing system
    from app.models import Session as ProcessingSession
    session = ProcessingSession(
        tenant_id=tenant_user.tenant_id,
        invoice_id=invoice.id,
        status="pending"
    )

    db.add(session)
    db.commit()
    db.refresh(session)

    # Start processing task
    from app.tasks.invoice_processing_tasks import process_invoice_task
    task = process_invoice_task.delay(
        invoice_id=str(invoice.id),
        tenant_id=str(tenant_user.tenant_id)
    )

    return {
        "id": invoice.id,
        "session_id": session.id,
        "task_id": task.id,
        "message": "Invoice uploaded successfully and processing started",
        "status": invoice.status
    }


@router.get("/", response_model=List[dict])
async def list_invoices(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
):
    """List invoices for current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    query = db.query(Invoice).filter(Invoice.tenant_id == tenant_user.tenant_id)
    
    if status_filter:
        query = query.filter(Invoice.status == status_filter)
    
    invoices = query.offset(skip).limit(limit).all()
    
    return [
        {
            "id": invoice.id,
            "supplier_name": invoice.supplier_name,
            "invoice_number": invoice.invoice_number,
            "total_amount": invoice.total_amount,
            "currency": invoice.currency,
            "status": invoice.status,
            "created_at": invoice.created_at,
            "original_filename": invoice.original_filename
        }
        for invoice in invoices
    ]


@router.get("/{invoice_id}")
async def get_invoice(
    invoice_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Get invoice details"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Get accounting entries
    accounting_entries = db.query(AccountingEntry).filter(
        AccountingEntry.invoice_id == invoice_id,
        AccountingEntry.tenant_id == tenant_user.tenant_id
    ).all()
    
    return {
        "id": invoice.id,
        "supplier_name": invoice.supplier_name,
        "invoice_number": invoice.invoice_number,
        "invoice_date": invoice.invoice_date,
        "due_date": invoice.due_date,
        "total_amount": invoice.total_amount,
        "currency": invoice.currency,
        "status": invoice.status,
        "extracted_text": invoice.extracted_text,
        "extracted_context": invoice.extracted_context,
        "processing_error": invoice.processing_error,
        "created_at": invoice.created_at,
        "updated_at": invoice.updated_at,
        "original_filename": invoice.original_filename,
        "accounting_entries": [
            {
                "id": entry.id,
                "account_code": entry.account_code,
                "account_name": entry.account_name,
                "debit_amount": entry.debit_amount,
                "credit_amount": entry.credit_amount,
                "description": entry.description,
                "confidence_score": entry.confidence_score,
                "is_validated": entry.is_validated
            }
            for entry in accounting_entries
        ]
    }


@router.put("/{invoice_id}/validate")
async def validate_invoice(
    invoice_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Validate invoice accounting entries"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACCOUNTING_VALIDATE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Update accounting entries as validated
    accounting_entries = db.query(AccountingEntry).filter(
        AccountingEntry.invoice_id == invoice_id,
        AccountingEntry.tenant_id == tenant_user.tenant_id
    ).all()
    
    for entry in accounting_entries:
        entry.is_validated = True
        entry.validated_by = tenant_user.user_id
    
    # Update invoice status
    invoice.status = "completed"

    try:
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Invoice validated successfully"}








@router.delete("/{invoice_id}")
async def delete_invoice(
    invoice_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """
    Delete an invoice and all related data including:
    - Action items
    - Files
    - Related database entries (cascade deleted automatically)
    """
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_DELETE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()

    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )

    logger.info(f"Starting deletion of invoice {invoice_id} and related data")

    try:
        # Delete the physical file
        try:
            if invoice.file_path and os.path.exists(invoice.file_path):
                os.remove(invoice.file_path)
                logger.info(f"Deleted file: {invoice.file_path}")
        except Exception as e:
            logger.warning(f"Could not delete file {invoice.file_path}: {e}")
            # Continue even if file deletion fails

        # Delete the invoice (this will cascade delete accounting entries, vectors, etc.)
        try:
            db.delete(invoice)
            db.commit()
        except Exception as e:
            db.rollback()
            raise handle_database_error(e)

        logger.info(f"Successfully deleted invoice {invoice_id} and all related data")

        return {
            "message": "Invoice and all related data deleted successfully"
        }

    except Exception as e:
        logger.error(f"Error deleting invoice {invoice_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete invoice: {str(e)}"
        )
