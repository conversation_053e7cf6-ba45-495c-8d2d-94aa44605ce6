#!/usr/bin/env python3
"""
Test script to create a new invoice and session to verify the processing works
"""
import sys
import os
import base64

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models.invoice import Invoice, Session as ProcessingSession
from app.tasks.invoice_processing_tasks import process_invoice_task
from uuid import uuid4

def create_test_session():
    """Create a test invoice and session"""
    
    db = SessionLocal()
    
    try:
        print("🧪 Creating test invoice and session...")
        
        # Skapa en test-faktura
        test_file_content = "Test PDF content"
        file_data_base64 = base64.b64encode(test_file_content.encode()).decode('utf-8')
        
        # Använd en känd tenant_id (från debug_sessions.py)
        tenant_id = "94311644-a034-4967-87ea-bc46068ab6be"
        
        invoice = Invoice(
            tenant_id=tenant_id,
            import_typ="manuell",
            file_data=file_data_base64,
            metadata_ERP=None,
            supplier_name="Test Supplier",
            original_filename="test_invoice.pdf",
            file_type="pdf",
            status="pending"
        )
        
        db.add(invoice)
        db.commit()
        db.refresh(invoice)
        print(f"✅ Created test invoice: {invoice.id}")
        
        # Skapa session
        session = ProcessingSession(
            tenant_id=tenant_id,
            invoice_id=invoice.id,
            status="pending"
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        print(f"✅ Created test session: {session.id}")
        
        # Starta processing task
        task = process_invoice_task.delay(
            invoice_id=str(invoice.id),
            tenant_id=str(tenant_id)
        )
        print(f"✅ Started processing task: {task.id}")
        
        return {
            "invoice_id": str(invoice.id),
            "session_id": str(session.id),
            "task_id": task.id
        }
        
    except Exception as e:
        db.rollback()
        print(f"❌ Error: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    result = create_test_session()
    print(f"\n🎉 Test session created successfully!")
    print(f"Invoice ID: {result['invoice_id']}")
    print(f"Session ID: {result['session_id']}")
    print(f"Task ID: {result['task_id']}")
