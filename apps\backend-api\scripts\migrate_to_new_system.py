#!/usr/bin/env python3
"""
Script för att migrera befintliga fakturor till det nya systemet
"""

import os
import sys
import base64
import logging
from pathlib import Path

# Lägg till backend-api till Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config import settings
from app.models import Invoice, Session as ProcessingSession

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def migrate_invoices():
    """Migrera befintliga fakturor till det nya systemet"""
    
    # Skapa databasanslutning
    engine = create_engine(settings.database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        logger.info("Starting migration of existing invoices to new system...")
        
        # Hämta alla befintliga fakturor som inte har file_data
        invoices = db.execute(text("""
            SELECT id, tenant_id, file_path_deprecated, original_filename, file_type, supplier_name
            FROM invoices
            WHERE file_data IS NULL AND file_path_deprecated IS NOT NULL AND file_path_deprecated != ''
        """)).fetchall()
        
        logger.info(f"Found {len(invoices)} invoices to migrate")
        
        migrated_count = 0
        error_count = 0
        
        for invoice_row in invoices:
            try:
                invoice_id = invoice_row.id
                file_path = invoice_row.file_path_deprecated
                
                logger.info(f"Migrating invoice {invoice_id} with file {file_path}")
                
                # Kontrollera om filen finns
                if not os.path.exists(file_path):
                    logger.warning(f"File not found: {file_path}, skipping invoice {invoice_id}")
                    continue
                
                # Läs filen och konvertera till base64
                try:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()
                    file_data_base64 = base64.b64encode(file_content).decode('utf-8')
                except Exception as e:
                    logger.error(f"Error reading file {file_path}: {e}")
                    error_count += 1
                    continue
                
                # Uppdatera fakturan med nya fält
                db.execute(text("""
                    UPDATE invoices 
                    SET 
                        import_typ = 'manuell',
                        file_data = :file_data,
                        metadata_ERP = NULL
                    WHERE id = :invoice_id
                """), {
                    'file_data': file_data_base64,
                    'invoice_id': invoice_id
                })
                
                # Skapa session för fakturan
                db.execute(text("""
                    INSERT INTO sessions (id, tenant_id, invoice_id, status, created_at, updated_at)
                    VALUES (gen_random_uuid(), :tenant_id, :invoice_id, 'pending', NOW(), NOW())
                """), {
                    'tenant_id': invoice_row.tenant_id,
                    'invoice_id': invoice_id
                })
                
                migrated_count += 1
                logger.info(f"Successfully migrated invoice {invoice_id}")
                
                # Commit efter varje faktura för att undvika stora transaktioner
                db.commit()
                
            except Exception as e:
                logger.error(f"Error migrating invoice {invoice_id}: {e}")
                db.rollback()
                error_count += 1
                continue
        
        logger.info(f"Migration completed. Migrated: {migrated_count}, Errors: {error_count}")
        
        # Uppdatera file_data för fakturor som inte har någon fil
        logger.info("Setting empty file_data for invoices without files...")
        
        result = db.execute(text("""
            UPDATE invoices 
            SET file_data = ''
            WHERE file_data IS NULL
        """))
        
        db.commit()
        logger.info(f"Updated {result.rowcount} invoices with empty file_data")
        
        # Nu kan vi göra file_data NOT NULL
        logger.info("Making file_data column NOT NULL...")
        db.execute(text("ALTER TABLE invoices ALTER COLUMN file_data SET NOT NULL"))
        db.commit()
        
        logger.info("Migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def cleanup_old_files():
    """Rensa gamla filer efter migrering (valfritt)"""
    logger.info("Cleanup of old files can be done manually if needed")
    logger.info("Old files are still available in their original locations")


if __name__ == "__main__":
    try:
        migrate_invoices()
        print("\n✅ Migration completed successfully!")
        print("\nNext steps:")
        print("1. Run the Alembic migration: alembic upgrade head")
        print("2. Test the new system")
        print("3. Optionally clean up old files")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)
