import pytest
import asyncio
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import tempfile
import os

from app.main import app
from app.database import Base, get_db
from app.models import *
from app.config import settings

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False}
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test"""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database override"""
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def temp_upload_dir():
    """Create a temporary upload directory"""
    with tempfile.TemporaryDirectory() as temp_dir:
        original_upload_dir = settings.upload_dir
        settings.upload_dir = temp_dir
        yield temp_dir
        settings.upload_dir = original_upload_dir


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "is_active": True,
        "is_superuser": False,
        "is_2fa_enabled": False
    }


@pytest.fixture
def sample_tenant_data():
    """Sample tenant data for testing"""
    return {
        "name": "Test Tenant",
        "domain": "test.example.com",
        "is_active": True
    }


@pytest.fixture
def sample_invoice_data():
    """Sample invoice data for testing"""
    return {
        "supplier_name": "Test Supplier",
        "invoice_number": "INV-001",
        "invoice_date": "2024-01-15",
        "due_date": "2024-02-15",
        "total_amount": 1500.00,
        "currency": "SEK",
        "original_filename": "test_invoice.pdf",
        "file_path": "/tmp/test_invoice.pdf",
        "file_type": "pdf",
        "status": "pending"
    }


@pytest.fixture
def sample_pdf_file():
    """Create a sample PDF file for testing"""
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Invoice) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000074 00000 n 
0000000120 00000 n 
0000000179 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
273
%%EOF"""
    
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
        f.write(pdf_content)
        f.flush()
        yield f.name
    
    # Cleanup
    try:
        os.unlink(f.name)
    except FileNotFoundError:
        pass


@pytest.fixture
def authenticated_headers(client, db_session, sample_user_data, sample_tenant_data):
    """Create authenticated headers for API requests"""
    from app.models.user import User, Role, TenantUser
    from app.models.tenant import Tenant
    from app.utils.security import get_password_hash, create_access_token
    
    # Create tenant
    tenant = Tenant(**sample_tenant_data)
    db_session.add(tenant)
    db_session.commit()
    db_session.refresh(tenant)
    
    # Create role
    role = Role(
        name="admin",
        description="Administrator",
        permissions=["invoices:read", "invoices:write", "invoices:delete"]
    )
    db_session.add(role)
    db_session.commit()
    db_session.refresh(role)
    
    # Create user
    user_data = sample_user_data.copy()
    user_data["hashed_password"] = get_password_hash(user_data.pop("password"))
    user = User(**user_data)
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    
    # Create tenant user relationship
    tenant_user = TenantUser(
        user_id=user.id,
        tenant_id=tenant.id,
        role_id=role.id,
        is_active=True
    )
    db_session.add(tenant_user)
    db_session.commit()
    
    # Create access token
    access_token = create_access_token({"sub": str(user.id)})
    
    return {
        "Authorization": f"Bearer {access_token}",
        "X-Tenant-ID": str(tenant.id)
    }, user, tenant, role


# Integration-specific fixtures
@pytest.fixture
def test_fortnox_integration(db_session, sample_tenant_data):
    """Create test Fortnox integration"""
    from app.models.tenant import Tenant
    from app.models.integration import InvoiceIntegration
    from uuid import uuid4

    # Create tenant if not exists
    tenant = Tenant(**sample_tenant_data)
    db_session.add(tenant)
    db_session.commit()
    db_session.refresh(tenant)

    integration = InvoiceIntegration(
        id=uuid4(),
        tenant_id=tenant.id,
        integration_type="FORTNOX",
        configuration={
            "client_id": "test-fortnox-client-id",
            "client_secret": "test-fortnox-client-secret"
        },
        name="Test Fortnox Integration",
        description="Test integration for Fortnox",
        is_active=True
    )

    db_session.add(integration)
    db_session.commit()
    db_session.refresh(integration)

    return integration, tenant


@pytest.fixture
def test_oauth_token(db_session, test_fortnox_integration):
    """Create test OAuth2 token"""
    from app.models.integration import OAuth2Token
    from uuid import uuid4
    from datetime import datetime, timedelta

    integration, tenant = test_fortnox_integration

    token = OAuth2Token(
        id=uuid4(),
        tenant_id=tenant.id,
        integration_id=integration.id,
        provider="FORTNOX",
        access_token="test-access-token",
        refresh_token="test-refresh-token",
        token_type="Bearer",
        expires_at=datetime.utcnow() + timedelta(hours=1),
        scope="invoice",
        is_active=True
    )

    db_session.add(token)
    db_session.commit()
    db_session.refresh(token)

    return token


@pytest.fixture
def sample_integration_data():
    """Sample integration data for testing"""
    return {
        "fortnox": {
            "integration_type": "FORTNOX",
            "name": "Test Fortnox",
            "configuration": {
                "client_id": "test-fortnox-client-id",
                "client_secret": "test-fortnox-client-secret"
            }
        },
        "visma": {
            "integration_type": "VISMA",
            "name": "Test Visma",
            "configuration": {
                "client_id": "test-visma-client-id",
                "client_secret": "test-visma-client-secret"
            }
        },
        "http": {
            "integration_type": "HTTP",
            "name": "Test HTTP",
            "configuration": {
                "base_url": "https://example.com/invoices",
                "auth_type": "none"
            }
        }
    }
