"""Add invoice integrations and OAuth2 tokens

Revision ID: 007
Revises: 006
Create Date: 2024-01-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '007'
down_revision = '006'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create invoice_integrations table
    op.create_table('invoice_integrations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('integration_type', sa.String(length=50), nullable=False),
        sa.Column('configuration', sa.Text(), nullable=False),  # Encrypted JSON
        sa.Column('is_active', sa.<PERSON>an(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=True),
        sa.Column('description', sa.String(length=500), nullable=True),
        sa.Column('last_sync_at', sa.DateTime(), nullable=True),
        sa.Column('last_sync_status', sa.String(length=50), nullable=True),
        sa.Column('last_error', sa.String(length=1000), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_invoice_integrations_tenant_id'), 'invoice_integrations', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_invoice_integrations_integration_type'), 'invoice_integrations', ['integration_type'], unique=False)
    op.create_index(op.f('ix_invoice_integrations_is_active'), 'invoice_integrations', ['is_active'], unique=False)

    # Create oauth2_tokens table
    op.create_table('oauth2_tokens',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('access_token', sa.Text(), nullable=False),  # Encrypted
        sa.Column('refresh_token', sa.Text(), nullable=True),  # Encrypted
        sa.Column('token_type', sa.String(length=50), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('scope', sa.String(length=500), nullable=True),
        sa.Column('extra_data', sa.Text(), nullable=True),  # Encrypted JSON
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['integration_id'], ['invoice_integrations.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_oauth2_tokens_tenant_id'), 'oauth2_tokens', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_oauth2_tokens_integration_id'), 'oauth2_tokens', ['integration_id'], unique=False)
    op.create_index(op.f('ix_oauth2_tokens_provider'), 'oauth2_tokens', ['provider'], unique=False)
    op.create_index(op.f('ix_oauth2_tokens_is_active'), 'oauth2_tokens', ['is_active'], unique=False)

    # Add default values for new columns
    op.execute("ALTER TABLE invoice_integrations ALTER COLUMN is_active SET DEFAULT true")
    op.execute("ALTER TABLE oauth2_tokens ALTER COLUMN token_type SET DEFAULT 'Bearer'")
    op.execute("ALTER TABLE oauth2_tokens ALTER COLUMN is_active SET DEFAULT true")

    # Enable RLS on new tables
    op.execute("ALTER TABLE invoice_integrations ENABLE ROW LEVEL SECURITY")
    op.execute("ALTER TABLE oauth2_tokens ENABLE ROW LEVEL SECURITY")

    # Create RLS policies for invoice_integrations
    op.execute("""
        CREATE POLICY invoice_integrations_tenant_isolation ON invoice_integrations
        USING (tenant_id = current_setting('app.current_tenant_id')::uuid)
    """)

    # Create RLS policies for oauth2_tokens
    op.execute("""
        CREATE POLICY oauth2_tokens_tenant_isolation ON oauth2_tokens
        USING (tenant_id = current_setting('app.current_tenant_id')::uuid)
    """)


def downgrade() -> None:
    # Drop RLS policies
    op.execute("DROP POLICY IF EXISTS oauth2_tokens_tenant_isolation ON oauth2_tokens")
    op.execute("DROP POLICY IF EXISTS invoice_integrations_tenant_isolation ON invoice_integrations")

    # Drop indexes
    op.drop_index(op.f('ix_oauth2_tokens_is_active'), table_name='oauth2_tokens')
    op.drop_index(op.f('ix_oauth2_tokens_provider'), table_name='oauth2_tokens')
    op.drop_index(op.f('ix_oauth2_tokens_integration_id'), table_name='oauth2_tokens')
    op.drop_index(op.f('ix_oauth2_tokens_tenant_id'), table_name='oauth2_tokens')
    
    op.drop_index(op.f('ix_invoice_integrations_is_active'), table_name='invoice_integrations')
    op.drop_index(op.f('ix_invoice_integrations_integration_type'), table_name='invoice_integrations')
    op.drop_index(op.f('ix_invoice_integrations_tenant_id'), table_name='invoice_integrations')

    # Drop tables
    op.drop_table('oauth2_tokens')
    op.drop_table('invoice_integrations')
