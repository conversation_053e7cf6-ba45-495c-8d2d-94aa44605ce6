"""
OAuth2 utilities for handling external provider authentication.
"""

import secrets
import hashlib
import base64
from typing import Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from urllib.parse import urlencode, parse_qs, urlparse
import httpx
import logging

from app.config import settings
from app.models.integration import OAuth2Token, InvoiceIntegration
from app.schemas.integration import OAuth2TokenSchema
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class OAuth2Provider:
    """Base OAuth2 provider configuration"""
    
    def __init__(self, client_id: str, client_secret: str, base_url: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = base_url
        self.scope = ""
        
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        """Generate authorization URL for OAuth2 flow"""
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "state": state,
            "scope": self.scope
        }
        return f"{self.base_url}/oauth/authorize?{urlencode(params)}"
    
    async def exchange_code_for_token(self, code: str, redirect_uri: str) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        data = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": redirect_uri
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/oauth/token",
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            response.raise_for_status()
            return response.json()
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh an expired access token"""
        data = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/oauth/token",
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            response.raise_for_status()
            return response.json()


class FortnoxOAuth2Provider(OAuth2Provider):
    """Fortnox-specific OAuth2 provider"""
    
    def __init__(self, client_id: str, client_secret: str):
        super().__init__(client_id, client_secret, "https://apps.fortnox.se")
        self.scope = "invoice"


class VismaOAuth2Provider(OAuth2Provider):
    """Visma eEkonomi-specific OAuth2 provider"""
    
    def __init__(self, client_id: str, client_secret: str):
        super().__init__(client_id, client_secret, "https://identity.vismaonline.com")
        self.scope = "ea:accounting"


class OAuth2Manager:
    """Manager for OAuth2 operations"""
    
    def __init__(self, db: Session):
        self.db = db
        
    def generate_state(self, integration_id: str) -> str:
        """Generate secure state parameter for OAuth2 flow"""
        random_bytes = secrets.token_bytes(32)
        state_data = f"{integration_id}:{random_bytes.hex()}"
        return base64.urlsafe_b64encode(state_data.encode()).decode()
    
    def verify_state(self, state: str, integration_id: str) -> bool:
        """Verify state parameter matches integration"""
        try:
            decoded = base64.urlsafe_b64decode(state.encode()).decode()
            stored_integration_id, _ = decoded.split(":", 1)
            return stored_integration_id == integration_id
        except Exception:
            return False
    
    def get_provider(self, provider_name: str) -> OAuth2Provider:
        """Get OAuth2 provider instance"""
        if provider_name.upper() == "FORTNOX":
            return FortnoxOAuth2Provider(
                settings.fortnox_client_id,
                settings.fortnox_client_secret
            )
        elif provider_name.upper() == "VISMA":
            return VismaOAuth2Provider(
                settings.visma_client_id,
                settings.visma_client_secret
            )
        else:
            raise ValueError(f"Unsupported OAuth2 provider: {provider_name}")
    
    async def create_authorization_url(self, integration_id: str, redirect_uri: str) -> Tuple[str, str]:
        """Create authorization URL for integration"""
        integration = self.db.query(InvoiceIntegration).filter(
            InvoiceIntegration.id == integration_id
        ).first()
        
        if not integration:
            raise ValueError("Integration not found")
        
        provider = self.get_provider(integration.integration_type)
        state = self.generate_state(str(integration_id))
        auth_url = provider.get_authorization_url(redirect_uri, state)
        
        return auth_url, state
    
    async def handle_callback(self, code: str, state: str, integration_id: str, redirect_uri: str) -> OAuth2Token:
        """Handle OAuth2 callback and store tokens"""
        if not self.verify_state(state, str(integration_id)):
            raise ValueError("Invalid state parameter")
        
        integration = self.db.query(InvoiceIntegration).filter(
            InvoiceIntegration.id == integration_id
        ).first()
        
        if not integration:
            raise ValueError("Integration not found")
        
        provider = self.get_provider(integration.integration_type)
        token_data = await provider.exchange_code_for_token(code, redirect_uri)
        
        # Calculate expiration time
        expires_at = None
        if "expires_in" in token_data:
            expires_at = datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
        
        # Store or update token
        existing_token = self.db.query(OAuth2Token).filter(
            OAuth2Token.integration_id == integration_id,
            OAuth2Token.provider == integration.integration_type
        ).first()
        
        if existing_token:
            existing_token.access_token = token_data["access_token"]
            existing_token.refresh_token = token_data.get("refresh_token")
            existing_token.expires_at = expires_at
            existing_token.scope = token_data.get("scope")
            existing_token.is_active = True
            token = existing_token
        else:
            token = OAuth2Token(
                tenant_id=integration.tenant_id,
                integration_id=integration_id,
                provider=integration.integration_type,
                access_token=token_data["access_token"],
                refresh_token=token_data.get("refresh_token"),
                expires_at=expires_at,
                scope=token_data.get("scope"),
                is_active=True
            )
            self.db.add(token)
        
        self.db.commit()
        self.db.refresh(token)
        
        return token
    
    async def refresh_token_if_needed(self, token: OAuth2Token) -> OAuth2Token:
        """Refresh token if it's expired or about to expire"""
        if not token.is_expired and token.expires_at:
            # Check if token expires within next 5 minutes
            if datetime.utcnow() + timedelta(minutes=5) < token.expires_at:
                return token
        
        if not token.refresh_token:
            raise ValueError("No refresh token available")
        
        provider = self.get_provider(token.provider)
        
        try:
            token_data = await provider.refresh_token(token.refresh_token)
            
            # Update token
            token.access_token = token_data["access_token"]
            if "refresh_token" in token_data:
                token.refresh_token = token_data["refresh_token"]
            
            if "expires_in" in token_data:
                token.expires_at = datetime.utcnow() + timedelta(seconds=token_data["expires_in"])
            
            token.scope = token_data.get("scope", token.scope)
            
            self.db.commit()
            self.db.refresh(token)
            
            logger.info(f"Refreshed OAuth2 token for {token.provider} integration {token.integration_id}")
            
        except Exception as e:
            logger.error(f"Failed to refresh token for {token.provider}: {e}")
            token.is_active = False
            self.db.commit()
            raise
        
        return token
    
    def get_valid_token(self, integration_id: str) -> Optional[OAuth2Token]:
        """Get valid token for integration, refreshing if necessary"""
        token = self.db.query(OAuth2Token).filter(
            OAuth2Token.integration_id == integration_id,
            OAuth2Token.is_active == True
        ).first()
        
        if not token:
            return None
        
        # This would need to be called in an async context
        # For now, just return the token and let the caller handle refresh
        return token
