/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',    // Väldigt ljusblå
          500: '#3b82f6',   // Mellanblå
          600: '#2563eb',   // Mörkare blå
          700: '#1d4ed8',   // Ännu mörkare blå
      } ,
        success: {
          50: '#e6f4ea',
          100: '#c2e4c7',
          200: '#9ccc9f',
          300: '#73b677',
          400: '#4ba050',
          500: '#2e8d34',   // Basgrön
          600: '#26752b',
          700: '#1d5d21',
          800: '#124419',
          900: '#0a2e12',
        },

        warning: {
          50: '#fff8e1',
          100: '#ffecb3',
          200: '#ffe082',
          300: '#ffd54f',
          400: '#ffca28',
          500: '#ffc107',   // Basgul
          600: '#e6ac06',
          700: '#b38a05',
          800: '#7f6304',
          900: '#4b3c02',
        },

        danger: {
          50: '#fdecea',
          100: '#f9c4c1',
          200: '#f99b98',
          300: '#f7726e',
          400: '#f54a48',
          500: '#ef2a21',   // Basröd
          600: '#d5201c',
          700: '#9f1813',
          800: '#6e100c',
          900: '#3e0806',
        }
      }
    },
  },
  plugins: [],
}
