"""
Custom encryption utilities for sensitive data.
"""

import json
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from sqlalchemy import TypeDecorator, Text
from typing import Any, Optional

from app.config import settings


class EncryptionManager:
    """Manages encryption and decryption of sensitive data"""
    
    def __init__(self, key: str):
        """Initialize with encryption key"""
        # Derive a proper Fernet key from the settings key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'aggie_salt_2024',  # Fixed salt for consistency
            iterations=100000,
        )
        key_bytes = kdf.derive(key.encode())
        fernet_key = base64.urlsafe_b64encode(key_bytes)
        self.fernet = Fernet(fernet_key)
    
    def encrypt(self, data: Any) -> str:
        """Encrypt data and return as base64 string"""
        if data is None:
            return None
        
        # Convert to JSON string if not already a string
        if isinstance(data, str):
            json_str = data
        else:
            json_str = json.dumps(data, default=str)
        
        # Encrypt and encode as base64
        encrypted_bytes = self.fernet.encrypt(json_str.encode('utf-8'))
        return base64.b64encode(encrypted_bytes).decode('utf-8')
    
    def decrypt(self, encrypted_data: str) -> Any:
        """Decrypt base64 string and return original data"""
        if encrypted_data is None:
            return None
        
        try:
            # Decode from base64 and decrypt
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            json_str = decrypted_bytes.decode('utf-8')
            
            # Try to parse as JSON, fallback to string
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                return json_str
                
        except Exception as e:
            # If decryption fails, return None or raise based on requirements
            raise ValueError(f"Failed to decrypt data: {e}")


# Global encryption manager instance
encryption_manager = EncryptionManager(settings.encryption_key)


class EncryptedType(TypeDecorator):
    """SQLAlchemy type decorator for encrypted fields"""
    
    impl = Text
    cache_ok = True
    
    def __init__(self, return_type=None, **kwargs):
        """Initialize with optional return type specification"""
        self.return_type = return_type
        super().__init__(**kwargs)
    
    def process_bind_param(self, value: Any, dialect) -> Optional[str]:
        """Encrypt value before storing in database"""
        if value is None:
            return None
        return encryption_manager.encrypt(value)
    
    def process_result_value(self, value: Optional[str], dialect) -> Any:
        """Decrypt value after retrieving from database"""
        if value is None:
            return None
        return encryption_manager.decrypt(value)


class EncryptedJSON(EncryptedType):
    """Encrypted JSON field type"""
    
    def __init__(self, **kwargs):
        super().__init__(return_type=dict, **kwargs)


class EncryptedString(EncryptedType):
    """Encrypted string field type"""
    
    def __init__(self, **kwargs):
        super().__init__(return_type=str, **kwargs)


# Utility functions for manual encryption/decryption
def encrypt_data(data: Any) -> str:
    """Encrypt data manually"""
    return encryption_manager.encrypt(data)


def decrypt_data(encrypted_data: str) -> Any:
    """Decrypt data manually"""
    return encryption_manager.decrypt(encrypted_data)


def test_encryption():
    """Test encryption functionality"""
    test_data = {
        "client_id": "test-client-id",
        "client_secret": "super-secret-value",
        "nested": {"key": "value", "number": 42}
    }
    
    # Test encryption/decryption
    encrypted = encrypt_data(test_data)
    decrypted = decrypt_data(encrypted)
    
    print(f"Original: {test_data}")
    print(f"Encrypted: {encrypted[:50]}...")
    print(f"Decrypted: {decrypted}")
    print(f"Match: {test_data == decrypted}")
    
    # Test string encryption
    test_string = "sensitive-token-12345"
    encrypted_string = encrypt_data(test_string)
    decrypted_string = decrypt_data(encrypted_string)
    
    print(f"String original: {test_string}")
    print(f"String encrypted: {encrypted_string[:50]}...")
    print(f"String decrypted: {decrypted_string}")
    print(f"String match: {test_string == decrypted_string}")


if __name__ == "__main__":
    test_encryption()
