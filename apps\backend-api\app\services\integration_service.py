"""
Service for managing invoice integrations and fetching invoices.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.integration import InvoiceIntegration
from app.models.tenant import Tenant
from app.integrations import FortnoxInvoiceFetcher, VismaInvoiceFetcher, SimpleHttpInvoiceFetcher
from app.integrations.base import InvoiceFetcher

logger = logging.getLogger(__name__)


class IntegrationService:
    """Service for managing invoice integrations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_active_integrations(self, tenant_id: str) -> List[InvoiceIntegration]:
        """Get all active integrations for a tenant"""
        return self.db.query(InvoiceIntegration).filter(
            InvoiceIntegration.tenant_id == tenant_id,
            InvoiceIntegration.is_active == True
        ).all()
    
    def create_fetcher(self, integration: InvoiceIntegration) -> InvoiceFetcher:
        """Create appropriate fetcher instance for integration"""
        import json

        # Handle configuration - it might be a string due to JSON column type
        if isinstance(integration.configuration, str):
            config = json.loads(integration.configuration)
        else:
            config = integration.configuration.copy()

        config["integration_id"] = str(integration.id)
        
        if integration.integration_type == "FORTNOX":
            return FortnoxInvoiceFetcher(str(integration.tenant_id), config, self.db)
        elif integration.integration_type == "VISMA":
            return VismaInvoiceFetcher(str(integration.tenant_id), config, self.db)
        elif integration.integration_type == "HTTP":
            return SimpleHttpInvoiceFetcher(str(integration.tenant_id), config)
        else:
            raise ValueError(f"Unsupported integration type: {integration.integration_type}")
    
    async def fetch_invoices_for_tenant(self, tenant_id: str, since: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Fetch invoices from all active integrations for a tenant.
        
        Args:
            tenant_id: Tenant ID to fetch invoices for
            since: Optional datetime to fetch invoices since
            
        Returns:
            Dictionary with fetch results
        """
        integrations = self.get_active_integrations(tenant_id)
        
        if not integrations:
            logger.info(f"No active integrations found for tenant {tenant_id}")
            return {
                "tenant_id": tenant_id,
                "integrations_processed": 0,
                "total_invoices": 0,
                "successful_integrations": 0,
                "failed_integrations": 0,
                "errors": []
            }
        
        results = {
            "tenant_id": tenant_id,
            "integrations_processed": len(integrations),
            "total_invoices": 0,
            "successful_integrations": 0,
            "failed_integrations": 0,
            "errors": [],
            "integration_results": []
        }
        
        since_str = since.isoformat() if since else None
        
        for integration in integrations:
            try:
                logger.info(f"Fetching invoices from {integration.integration_type} integration {integration.id}")
                
                fetcher = self.create_fetcher(integration)
                invoices = await fetcher.fetch_invoices(since_str)
                
                integration_result = {
                    "integration_id": str(integration.id),
                    "integration_type": integration.integration_type,
                    "invoices_count": len(invoices),
                    "success": True,
                    "error": None,
                    "invoices": invoices
                }
                
                results["integration_results"].append(integration_result)
                results["total_invoices"] += len(invoices)
                results["successful_integrations"] += 1
                
                # Update integration sync status
                integration.last_sync_at = datetime.utcnow()
                integration.last_sync_status = "success"
                integration.last_error = None
                
                logger.info(f"Successfully fetched {len(invoices)} invoices from {integration.integration_type}")
                
            except Exception as e:
                error_msg = f"Error fetching from {integration.integration_type} integration {integration.id}: {str(e)}"
                logger.error(error_msg, exc_info=True)
                
                integration_result = {
                    "integration_id": str(integration.id),
                    "integration_type": integration.integration_type,
                    "invoices_count": 0,
                    "success": False,
                    "error": str(e),
                    "invoices": []
                }
                
                results["integration_results"].append(integration_result)
                results["failed_integrations"] += 1
                results["errors"].append(error_msg)
                
                # Update integration sync status
                integration.last_sync_at = datetime.utcnow()
                integration.last_sync_status = "failed"
                integration.last_error = str(e)
        
        # Commit integration status updates
        try:
            self.db.commit()
        except Exception as e:
            logger.error(f"Error updating integration sync status: {e}")
            self.db.rollback()
        
        return results
    
    async def test_integration(self, integration_id: str) -> Dict[str, Any]:
        """
        Test a specific integration.
        
        Args:
            integration_id: Integration ID to test
            
        Returns:
            Test results
        """
        integration = self.db.query(InvoiceIntegration).filter(
            InvoiceIntegration.id == integration_id
        ).first()
        
        if not integration:
            return {
                "success": False,
                "message": "Integration not found",
                "details": {"integration_id": integration_id}
            }
        
        try:
            fetcher = self.create_fetcher(integration)
            result = await fetcher.test_connection()
            
            logger.info(f"Integration test for {integration.integration_type} {integration_id}: {result['success']}")
            return result
            
        except Exception as e:
            logger.error(f"Error testing integration {integration_id}: {e}")
            return {
                "success": False,
                "message": f"Integration test failed: {str(e)}",
                "details": {
                    "integration_id": integration_id,
                    "error_type": type(e).__name__
                }
            }
    
    async def send_post_processing_callback(self, integration_id: str, invoice_id: str, 
                                          status: str, data: Dict[str, Any] = None) -> bool:
        """
        Send post-processing callback to integration.
        
        Args:
            integration_id: Integration ID
            invoice_id: External invoice ID
            status: Processing status
            data: Additional processing data
            
        Returns:
            True if callback was successful
        """
        try:
            integration = self.db.query(InvoiceIntegration).filter(
                InvoiceIntegration.id == integration_id
            ).first()
            
            if not integration:
                logger.error(f"Integration {integration_id} not found for callback")
                return False
            
            fetcher = self.create_fetcher(integration)
            result = await fetcher.post_processing_callback(invoice_id, status, data)
            
            logger.info(f"Post-processing callback for {integration.integration_type} {integration_id}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error sending callback to integration {integration_id}: {e}")
            return False
    
    def get_integration_stats(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get integration statistics for a tenant.
        
        Args:
            tenant_id: Tenant ID
            
        Returns:
            Integration statistics
        """
        integrations = self.db.query(InvoiceIntegration).filter(
            InvoiceIntegration.tenant_id == tenant_id
        ).all()
        
        stats = {
            "total_integrations": len(integrations),
            "active_integrations": 0,
            "inactive_integrations": 0,
            "by_type": {},
            "last_sync_times": {},
            "sync_status": {}
        }
        
        for integration in integrations:
            if integration.is_active:
                stats["active_integrations"] += 1
            else:
                stats["inactive_integrations"] += 1
            
            # Count by type
            int_type = integration.integration_type
            if int_type not in stats["by_type"]:
                stats["by_type"][int_type] = 0
            stats["by_type"][int_type] += 1
            
            # Track sync times and status
            if integration.last_sync_at:
                stats["last_sync_times"][str(integration.id)] = integration.last_sync_at.isoformat()
            
            if integration.last_sync_status:
                stats["sync_status"][str(integration.id)] = integration.last_sync_status
        
        return stats
    
    async def sync_all_tenant_integrations(self, tenant_id: str, since: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Convenience method to sync all integrations for a tenant and return processed invoices.
        
        Args:
            tenant_id: Tenant ID
            since: Optional datetime to fetch invoices since
            
        Returns:
            Sync results with processed invoices
        """
        results = await self.fetch_invoices_for_tenant(tenant_id, since)
        
        # Flatten all invoices from all integrations
        all_invoices = []
        for integration_result in results.get("integration_results", []):
            if integration_result["success"]:
                all_invoices.extend(integration_result["invoices"])
        
        return {
            "sync_results": results,
            "invoices": all_invoices
        }
