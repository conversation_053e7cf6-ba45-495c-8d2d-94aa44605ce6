# Deployment Guide

## Prerequisites

- Docker and Docker Compose
- Domain name with SSL certificate (for production)
- OpenAI API key or Azure OpenAI access
- SMTP server for email notifications (optional)

## Development Deployment

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository>
cd aggie
cp .env.example .env
```

### 2. Configure Environment

Edit `.env` file with your settings:

```bash
# Required
OPENAI_API_KEY=your-openai-api-key-here

# Optional - Email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
```

### 3. Start Services

```bash
# Using Docker Compose
docker-compose up -d

# Or using Makefile
make dev
```

### 4. Initialize Database

```bash
# Run migrations
make migrate

# Or manually
docker-compose exec api alembic upgrade head
```

### 5. Access Application

- Frontend: http://localhost:3000
- API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Production Deployment

### 1. Server Setup

Recommended server specifications:
- 4+ CPU cores
- 8+ GB RAM
- 100+ GB SSD storage
- Ubuntu 20.04+ or similar

### 2. Install Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. Configure Environment

```bash
# Clone repository
git clone <repository>
cd aggie

# Copy and configure environment
cp .env.example .env
nano .env
```

Production environment variables:

```bash
# Database
POSTGRES_DB=aggie_prod
POSTGRES_USER=aggie_user
POSTGRES_PASSWORD=secure_random_password_here

# Redis
REDIS_PASSWORD=secure_redis_password_here

# Security (generate secure keys)
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here-at-least-32-chars
ENCRYPTION_KEY=your-32-character-encryption-key-here

# LLM Configuration
LLM_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key-here

# Email Configuration
SMTP_HOST=smtp.yourprovider.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-secure-password
SMTP_FROM_EMAIL=<EMAIL>

# Frontend
FRONTEND_API_URL=https://api.yourcompany.com
```

### 4. SSL Certificate Setup

#### Option A: Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt install certbot

# Generate certificate
sudo certbot certonly --standalone -d api.yourcompany.com -d yourcompany.com

# Copy certificates
sudo mkdir -p ./ssl
sudo cp /etc/letsencrypt/live/yourcompany.com/fullchain.pem ./ssl/
sudo cp /etc/letsencrypt/live/yourcompany.com/privkey.pem ./ssl/
sudo chown -R $USER:$USER ./ssl
```

#### Option B: Custom SSL Certificate

```bash
mkdir -p ./ssl
# Copy your SSL certificate files
cp your-certificate.crt ./ssl/fullchain.pem
cp your-private-key.key ./ssl/privkey.pem
```

### 5. Configure Nginx

Update `nginx.conf` for production:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream api {
        server api:8000;
    }

    upstream frontend {
        server frontend:80;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name yourcompany.com api.yourcompany.com;
        return 301 https://$server_name$request_uri;
    }

    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name yourcompany.com;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        location / {
            proxy_pass http://frontend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # API server
    server {
        listen 443 ssl http2;
        server_name api.yourcompany.com;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # API routes
        location / {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Auth routes with stricter rate limiting
        location /auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://api/auth/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # File upload size limit
        client_max_body_size 50M;
    }
}
```

### 6. Deploy Application

```bash
# Build and start production services
docker-compose -f docker-compose.prod.yml up -d --build

# Or using Makefile
make prod
```

### 7. Initialize Database

```bash
# Run migrations
docker-compose -f docker-compose.prod.yml exec api alembic upgrade head

# Create initial admin user (optional)
docker-compose -f docker-compose.prod.yml exec api python -c "
from app.database import SessionLocal
from app.models.user import User, Role, TenantUser
from app.models.tenant import Tenant
from app.utils.security import get_password_hash
import uuid

db = SessionLocal()

# Create tenant
tenant = Tenant(name='Default Tenant', is_active=True)
db.add(tenant)
db.commit()

# Create admin role
role = Role(
    name='admin',
    description='Administrator',
    permissions=['invoices:read', 'invoices:write', 'invoices:delete', 'users:read', 'users:write']
)
db.add(role)
db.commit()

# Create admin user
user = User(
    email='<EMAIL>',
    hashed_password=get_password_hash('change-this-password'),
    is_active=True,
    is_superuser=True
)
db.add(user)
db.commit()

# Link user to tenant
tenant_user = TenantUser(
    user_id=user.id,
    tenant_id=tenant.id,
    role_id=role.id,
    is_active=True
)
db.add(tenant_user)
db.commit()

print('Admin user created: <EMAIL> / change-this-password')
"
```

### 8. Verify Deployment

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs -f

# Test API health
curl https://api.yourcompany.com/health

# Test frontend
curl https://yourcompany.com
```

## Monitoring and Maintenance

### Health Checks

The application includes health check endpoints:

- API Health: `GET /health`
- Database connectivity is checked automatically

### Log Management

```bash
# View logs
docker-compose logs -f [service_name]

# Rotate logs (add to crontab)
docker system prune -f
```

### Backup Strategy

#### Database Backup

```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T db pg_dump -U aggie_user aggie_prod > backup_${DATE}.sql
# Upload to cloud storage or remote location
EOF

chmod +x backup.sh

# Add to crontab for daily backups
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

#### File Backup

```bash
# Backup uploaded files
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

### Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker-compose -f docker-compose.prod.yml up -d --build

# Run any new migrations
docker-compose -f docker-compose.prod.yml exec api alembic upgrade head
```

### Scaling

For high-traffic deployments:

1. **Horizontal Scaling**: Add more worker containers
2. **Database Scaling**: Use read replicas
3. **Load Balancing**: Use multiple API instances
4. **Caching**: Add Redis caching layer
5. **CDN**: Use CDN for static assets

### Security Considerations

1. **Firewall**: Configure UFW or iptables
2. **SSH**: Disable password authentication, use keys only
3. **Updates**: Keep system and Docker updated
4. **Monitoring**: Set up monitoring and alerting
5. **Secrets**: Use Docker secrets or external secret management

### Troubleshooting

Common issues and solutions:

1. **Port conflicts**: Check if ports 80/443 are available
2. **Permission issues**: Ensure proper file permissions
3. **SSL issues**: Verify certificate paths and validity
4. **Database connection**: Check database credentials and connectivity
5. **Memory issues**: Monitor resource usage and adjust limits

For support, check logs and refer to the troubleshooting section in the main README.
