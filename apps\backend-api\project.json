{"name": "backend-api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/backend-api", "projectType": "application", "targets": {"serve": {"executor": "nx:run-commands", "options": {"command": "uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload", "cwd": "apps/backend-api"}}, "test": {"executor": "nx:run-commands", "options": {"command": "python -m pytest", "cwd": "apps/backend-api"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "python -m flake8 .", "cwd": "apps/backend-api"}}}, "tags": ["scope:backend", "type:app"]}