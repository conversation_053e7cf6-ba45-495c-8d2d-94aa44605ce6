import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { CheckCircleIcon, ExclamationTriangleIcon, EyeIcon } from '@heroicons/react/24/outline';
import { actionItemsApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';
import { ActionItem } from '../types';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

export default function ActionItemsPage() {
  const [selectedPriority, setSelectedPriority] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showCompleted, setShowCompleted] = useState(false);
  const { currentTenant, hasPermission } = useTenant();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const { data: actionItems = [], isLoading, error } = useQuery(
    ['actionItems', currentTenant?.id, selectedPriority, selectedCategory, showCompleted],
    () => actionItemsApi.getActionItems({
      priority: selectedPriority || undefined,
      category: selectedCategory || undefined,
      completed: showCompleted ? undefined : false,
    }),
    {
      enabled: !!currentTenant,
      onError: (error: any) => {
        console.error('Action Items API Error:', error);
        console.log('Current tenant:', currentTenant);
        console.log('Tenant permissions:', currentTenant?.permissions);
        const message = error.response?.data?.detail || 'Failed to load action items';
        toast.error(message);
      }
    }
  );

  const reopenMutation = useMutation(
    (id: string) => actionItemsApi.reopenActionItem(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['actionItems']);
        toast.success('Action item reopened!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to reopen action item');
      },
    }
  );

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'review':
        return <CheckCircleIcon className="h-5 w-5 text-orange-500" />;
      default:
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!currentTenant) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a tenant to view action items.</p>
      </div>
    );
  }

  // Check if user has permission to read action items
  if (!hasPermission('action_items:read')) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-yellow-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
        <p className="mt-1 text-sm text-gray-500">
          You don't have permission to view action items.
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error Loading Action Items</h3>
        <p className="mt-1 text-sm text-gray-500">
          {(error as any)?.response?.data?.detail || 'Failed to load action items'}
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Action Items</h1>
          <p className="mt-2 text-sm text-gray-700">
            Tasks and items requiring attention for {currentTenant.name}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="mt-6">
        <div className="flex flex-wrap gap-4">
          <select
            value={selectedPriority}
            onChange={(e) => setSelectedPriority(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          >
            <option value="">All Priorities</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          >
            <option value="">All Categories</option>
            <option value="review">Review</option>
            <option value="validation">Validation</option>
            <option value="error">Error</option>
            <option value="manual_entry">Manual Entry</option>
          </select>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showCompleted}
              onChange={(e) => setShowCompleted(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
            <span className="ml-2 text-sm text-gray-700">Show completed</span>
          </label>
        </div>
      </div>

      {/* Action Items List */}
      <div className="mt-8">
        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
          </div>
        ) : actionItems.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No action items found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {actionItems.map((item: ActionItem) => (
              <div
                key={item.id}
                className={`bg-white shadow rounded-lg p-6 ${
                  item.is_completed ? 'opacity-75' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getCategoryIcon(item.category)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 className={`text-lg font-medium ${
                          item.is_completed ? 'line-through text-gray-500' : 'text-gray-900'
                        }`}>
                          {item.title}
                        </h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(item.priority)}`}>
                          {item.priority}
                        </span>
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                          {item.category}
                        </span>
                      </div>
                      <p className="mt-2 text-sm text-gray-600">
                        {item.description}
                      </p>
                      <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                        <span>Created: {formatDate(item.created_at)}</span>
                        {item.assigned_user && (
                          <span>Assigned to: {item.assigned_user.email}</span>
                        )}
                        {item.invoice && (
                          <span>Invoice: {item.invoice.supplier_name}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex-shrink-0 flex space-x-2">
                    {item.invoice && (
                      <button
                        onClick={() => navigate(`/action-items/${item.id}/details`)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <EyeIcon className="h-4 w-4 mr-1" />
                        View Details
                      </button>
                    )}
                    {item.is_completed && hasPermission('action_items:write') && (
                      <button
                        onClick={() => reopenMutation.mutate(item.id)}
                        disabled={reopenMutation.isLoading}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                      >
                        Reopen
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
