# Architecture Documentation

## System Overview

Aggie is an AI-powered invoice management platform built with a multi-tenant architecture. The system uses modern AI techniques including OCR, LLMs, and RAG (Retrieval-Augmented Generation) to automate invoice processing and provide accounting suggestions.

## Architecture Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  React Frontend │────▶│  FastAPI Backend│────▶│  PostgreSQL DB  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │  ▲
                               │  │
                               ▼  │
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Redis + Celery │◀───▶│  OpenAI / Azure │     │  File Storage   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Key Components

### 1. Frontend (React + TypeScript)

- **React**: Component-based UI library
- **TypeScript**: Type-safe JavaScript
- **React Query**: Data fetching and caching
- **React Router**: Client-side routing
- **TailwindCSS**: Utility-first CSS framework
- **Headless UI**: Accessible UI components

### 2. Backend (FastAPI + Python)

- **FastAPI**: Modern, high-performance web framework
- **SQLAlchemy**: ORM for database interactions
- **Alembic**: Database migration tool
- **Pydantic**: Data validation and settings management
- **Python-Jose**: JWT token handling
- **Passlib**: Password hashing
- **PyOTP**: TOTP-based 2FA

### 3. Database (PostgreSQL)

- **PostgreSQL**: Relational database
- **pgvector**: Vector similarity search extension
- **Row-Level Security (RLS)**: Tenant isolation at the database level
- **UUID**: Primary keys for all entities

### 4. AI Components

- **OCR Pipeline**: 
  - Tesseract OCR for image-based invoices
  - PyPDF2 for text extraction from PDFs
  - PDF2Image for converting PDFs to images when needed

- **LLM Integration**:
  - OpenAI GPT-4o-mini for text processing
  - Azure OpenAI alternative implementation
  - Modular provider design for easy switching

- **RAG System**:
  - Vector embeddings for semantic search
  - pgvector for efficient similarity queries
  - Context-aware accounting suggestions

### 5. Background Processing

- **Celery**: Distributed task queue
- **Redis**: Message broker and result backend
- **Task Scheduling**: Periodic tasks for maintenance

### 6. Security Features

- **JWT Authentication**: Secure token-based auth
- **TOTP 2FA**: Time-based one-time passwords
- **Row-Level Security**: Database-level tenant isolation
- **Permission System**: Fine-grained access control
- **Encrypted Storage**: Sensitive data encrypted at rest

## Multi-Tenancy Implementation

Aggie uses a multi-tenant architecture with database-level isolation:

1. **Shared Database, Separate Schemas**: All tenants share the same database but with logical separation.
2. **Row-Level Security (RLS)**: PostgreSQL RLS policies enforce tenant isolation at the database level.
3. **Tenant Context**: Each request includes a tenant ID header that sets the database context.
4. **Permission System**: Role-based access control within each tenant.

### RLS Implementation

```sql
-- Example RLS policy
CREATE POLICY tenant_isolation_policy ON invoices
FOR ALL
TO PUBLIC
USING (tenant_id::text = current_setting('app.current_tenant_id', true))
WITH CHECK (tenant_id::text = current_setting('app.current_tenant_id', true))
```

## Data Flow

### Invoice Processing Flow

1. **Upload**: User uploads invoice file (PDF, PNG, JPG)
2. **Queue**: Task is queued in Celery
3. **OCR**: Text is extracted from the document
4. **LLM Processing**: Structured data is extracted from text
5. **RAG**: Similar invoices are retrieved for context
6. **Accounting Suggestions**: AI generates accounting entries
7. **Confidence Check**: System determines if human review is needed
8. **Action Items**: Created for low-confidence results
9. **Notification**: Users are notified of items requiring attention

```
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│         │    │         │    │         │    │         │    │         │
│ Upload  │───▶│   OCR   │───▶│   LLM   │───▶│   RAG   │───▶│ Suggest │
│         │    │         │    │         │    │         │    │         │
└─────────┘    └─────────┘    └─────────┘    └─────────┘    └─────────┘
                                                                 │
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐        │
│         │    │         │    │         │    │         │        │
│ Notify  │◀───│ Action  │◀───│ Review  │◀───│ Check   │◀───────┘
│         │    │  Item   │    │ Needed? │    │ Conf.   │
└─────────┘    └─────────┘    └─────────┘    └─────────┘
```

## Database Schema

### Core Tables

1. **tenants**: Multi-tenant isolation
2. **users**: User accounts with 2FA support
3. **roles**: Role definitions with permissions
4. **tenant_users**: User-tenant-role relationships
5. **invoices**: Invoice metadata and processing status
6. **accounting_entries**: AI-suggested accounting entries
7. **invoice_vectors**: Vector embeddings for RAG
8. **action_items**: Human-in-the-loop workflow items

### Key Relationships

```
tenants 1──┐
           │
           ├──* tenant_users *──┐
           │                    │
users 1────┘                    │
                                │
roles 1───────────────────────┘
                                
tenants 1──┐                    
           │                    
           ├──* invoices 1──┐   
                            │   
                            ├──* accounting_entries
                            │   
                            └──* invoice_vectors
                            
tenants 1──┐                    
           │                    
           ├──* action_items    
           │                    
users 1────┘                    
```

## Authentication Flow

### Standard Login

1. User submits email/password
2. Backend validates credentials
3. JWT access token is generated
4. Frontend stores token in localStorage
5. Token is included in subsequent requests

### 2FA Login

1. User submits email/password
2. Backend validates credentials
3. Temporary token is generated
4. User enters TOTP code
5. Backend validates TOTP code
6. Full JWT access token is generated
7. Frontend stores token in localStorage

## Security Considerations

1. **Data Isolation**: Row-Level Security ensures tenants cannot access each other's data
2. **Authentication**: JWT tokens with short expiration and refresh token rotation
3. **2FA**: TOTP-based two-factor authentication with recovery codes
4. **Encryption**: Sensitive data encrypted at rest
5. **Rate Limiting**: Prevents brute force and DoS attacks
6. **Input Validation**: Pydantic models validate all inputs
7. **HTTPS**: All production traffic uses TLS/SSL
8. **Headers**: Security headers prevent common web vulnerabilities

## Scalability Considerations

1. **Stateless Backend**: Allows horizontal scaling
2. **Task Queue**: Offloads processing to worker nodes
3. **Database Indexing**: Optimized for multi-tenant queries
4. **Caching**: Redis caching for frequently accessed data
5. **Containerization**: Docker enables easy scaling and deployment

## Monitoring and Observability

1. **Logging**: Structured logging with context
2. **Health Checks**: API endpoints for monitoring
3. **Performance Metrics**: Request timing headers
4. **Error Tracking**: Global exception handler with detailed logging

## Development Workflow

1. **Local Development**: Docker Compose for local environment
2. **Testing**: Pytest for backend, Jest for frontend
3. **CI/CD**: Automated testing and deployment
4. **Migrations**: Alembic for database schema changes

## Future Enhancements

1. **Advanced Analytics**: Dashboard with processing metrics
2. **API Integrations**: Connect with accounting software
3. **Document Classification**: Auto-categorize different document types
4. **Multi-Language Support**: Process invoices in multiple languages
5. **Custom Workflows**: User-defined processing rules
6. **Mobile App**: Native mobile experience
