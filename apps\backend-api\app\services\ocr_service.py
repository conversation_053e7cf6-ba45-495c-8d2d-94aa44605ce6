# import pytesseract  # Removed - using multimodal LLM/external API instead
from PIL import Image
import fitz  # PyMuPDF
from pypdf import PdfReader  # Replaced PyPDF2 with pypdf
import io
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


class OCRService:
    """Service for extracting text from PDFs and preparing images for LLM processing"""

    def __init__(self):
        pass

    def process_file_for_llm(self, file_data_base64: str, file_type: str) -> Tuple[Optional[str], Optional[str]]:
        """Process file for LLM - returns (text_content, image_data_base64)

        For PDFs: Try to extract text first. If that fails or yields minimal text,
                 convert to images for visual analysis by LLM
        For images: Return (None, base64_image_data)

        Args:
            file_data_base64: Base64 encoded file data
            file_type: File type (pdf, jpg, png, etc.)

        Returns:
            tuple: (text_content, image_data_base64) - one will be None
        """
        try:
            if file_type.lower() == 'pdf':
                text_content = self._extract_text_from_pdf_base64(file_data_base64)

                # Check if we got meaningful text (not an error message)
                if (text_content and
                    len(text_content.strip()) > 10 and
                    not text_content.startswith("PDF innehåller ingen läsbar text") and
                    not text_content.startswith("PDF kräver OCR")):
                    logger.info(f"Successfully extracted {len(text_content)} characters from PDF")
                    return (text_content, None)
                else:
                    # Text extraction failed or yielded minimal text
                    # Try to convert PDF to images for visual analysis by LLM
                    logger.info("PDF text extraction failed or yielded minimal text, attempting to convert to images for LLM")
                    try:
                        image_data = self._convert_pdf_to_image_for_llm(file_data_base64)
                        return (None, image_data)
                    except Exception as e:
                        logger.error(f"PDF to image conversion failed: {e}")
                        # Check for specific error types to provide better error messages
                        error_str = str(e).lower()
                        if "failed to open" in error_str or "invalid pdf header" in error_str:
                            error_message = "PDF-filen verkar vara korrupt eller skadad och kan inte öppnas för bearbetning. Kontrollera att filen är en giltig PDF."
                        elif "stream has ended" in error_str:
                            error_message = "PDF-filen är ofullständig eller skadad. Kontrollera att hela filen har laddats upp korrekt."
                        else:
                            error_message = f"PDF innehåller ingen läsbar text och kunde inte konverteras till bild för visuell analys. Tekniskt fel: {str(e)}"
                        return (error_message, None)

            elif file_type.lower() in ['png', 'jpg', 'jpeg']:
                # För bilder returnerar vi base64 data direkt för LLM
                # Fix padding om det behövs
                fixed_base64 = self._fix_base64_padding(file_data_base64)
                return (None, fixed_base64)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
        except Exception as e:
            logger.error(f"Error processing file of type {file_type}: {e}")
            raise

    def _extract_text_from_pdf_base64(self, file_data_base64: str) -> str:
        """Extract text from PDF given base64 data"""
        import base64
        import tempfile
        import os

        try:
            # Fix base64 padding if needed
            file_data_base64 = self._fix_base64_padding(file_data_base64)

            # Decode base64 to bytes
            pdf_bytes = base64.b64decode(file_data_base64)

            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(pdf_bytes)
                temp_file_path = temp_file.name

            try:
                # Extract text using existing method
                text = self._extract_from_pdf(temp_file_path)
                return text
            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error extracting text from PDF base64: {e}")
            raise

    def _fix_base64_padding(self, base64_string: str) -> str:
        """Fix base64 padding if needed"""
        # Remove any whitespace
        base64_string = base64_string.strip()

        # Add padding if needed
        missing_padding = len(base64_string) % 4
        if missing_padding:
            base64_string += '=' * (4 - missing_padding)

        return base64_string
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file

        Returns:
            str: Extracted text, or empty string if no meaningful text found
        """
        text = ""

        # First try to extract text directly (for PDFs with selectable text)
        try:
            pdf_reader = PdfReader(file_path)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text.strip():
                    text += page_text + "\n"

            # If we got meaningful text, return it
            if len(text.strip()) > 10:  # Lower threshold
                logger.info(f"Extracted text directly from PDF: {len(text)} characters")
                return text.strip()
            else:
                logger.info("PDF appears to be image-based or has minimal text")

        except Exception as e:
            error_str = str(e).lower()
            if "invalid pdf header" in error_str:
                logger.error(f"Invalid PDF file - corrupt or not a PDF: {e}")
            elif "stream has ended" in error_str:
                logger.error(f"Incomplete PDF file: {e}")
            else:
                logger.warning(f"Direct PDF text extraction failed: {e}")

        # If direct extraction failed or yielded little text, try external OCR
        try:
            logger.info("Attempting external OCR for PDF")
            return self._external_ocr_pdf(file_path)
        except Exception as e:
            logger.warning(f"External OCR failed: {e}")
            # Return whatever text we managed to extract, even if minimal
            if text.strip():
                logger.info("Returning partial text extraction from PDF")
                return text.strip()
            else:
                logger.warning("No text could be extracted from PDF - will fall back to image processing")
                return ""  # Return empty string instead of error message
    
    def _external_ocr_pdf(self, file_path: str) -> str:
        """Extract text from PDF using external OCR API or multimodal LLM

        Note: This method is not fully implemented yet. It will fall back to
        image processing for LLM visual analysis.
        """
        try:
            # Try to open PDF with PyMuPDF to check if conversion is possible
            doc = fitz.open(file_path)
            page_count = len(doc)
            doc.close()
            logger.info(f"PDF has {page_count} pages available for OCR processing")

            # External OCR not yet implemented - return empty string to trigger image fallback
            logger.warning("External OCR not yet implemented - falling back to image processing")
            return ""

            # TODO: Future implementation structure:
            # text = ""
            # doc = fitz.open(file_path)
            # for page_num in range(len(doc)):
            #     logger.debug(f"Processing page {page_num+1} of PDF with external OCR")
            #     page = doc.load_page(page_num)
            #     pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better quality
            #     img_data = pix.tobytes("jpeg")
            #
            #     # Call external OCR API or multimodal LLM
            #     page_text = await self._call_external_ocr_api(img_data)
            #     text += page_text + "\n"
            #
            # doc.close()
            # logger.info(f"External OCR extracted {len(text)} characters from PDF")
            # return text.strip()

        except Exception as e:
            logger.error(f"External OCR PDF extraction failed: {e}")
            raise

    def _convert_pdf_to_image_for_llm(self, file_data_base64: str) -> str:
        """Convert PDF to image(s) for LLM visual analysis using PyMuPDF

        Args:
            file_data_base64: Base64 encoded PDF data

        Returns:
            str: Base64 encoded image data (first page of PDF)

        Raises:
            Exception: If PDF to image conversion fails
        """
        import base64

        try:
            # Fix base64 padding if needed
            file_data_base64 = self._fix_base64_padding(file_data_base64)

            # Decode base64 to bytes
            pdf_bytes = base64.b64decode(file_data_base64)

            # Open PDF directly from bytes using PyMuPDF
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")

            if len(doc) == 0:
                raise ValueError("PDF contains no pages")

            logger.info(f"PDF has {len(doc)} pages, converting first page to image")

            # Get first page
            page = doc.load_page(0)  # Load first page (0-indexed)

            # Convert page to image with high resolution for better OCR
            # Matrix(2, 2) means 2x zoom = 144 DPI (72 * 2)
            mat = fitz.Matrix(2, 2)
            pix = page.get_pixmap(matrix=mat)

            # Convert to JPEG bytes
            img_bytes = pix.tobytes("jpeg")

            # Close document
            doc.close()

            # Encode to base64
            image_base64 = base64.b64encode(img_bytes).decode('utf-8')

            logger.info(f"Successfully converted PDF first page to image ({len(image_base64)} base64 chars)")
            return image_base64

        except Exception as e:
            logger.error(f"Error converting PDF to image for LLM: {e}")
            # Provide more specific error information
            error_str = str(e).lower()
            if "failed to open" in error_str:
                raise Exception("PDF-filen kunde inte öppnas - filen kan vara korrupt eller skadad")
            elif "invalid pdf" in error_str or "pdf header" in error_str:
                raise Exception("Ogiltig PDF-fil - filen verkar inte vara en korrekt formaterad PDF")
            elif "no pages" in error_str:
                raise Exception("PDF-filen innehåller inga sidor")
            else:
                raise Exception(f"Kunde inte konvertera PDF till bild: {str(e)}")
    
    def _extract_from_image(self, file_path: str) -> str:
        """Extract text from image file using external OCR API or multimodal LLM"""
        try:
            # Open and preprocess image
            image = Image.open(file_path)

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Enhance image for better OCR results
            image = self._preprocess_image(image)

            # TODO: Implement external OCR API call or multimodal LLM
            # For now, return a placeholder message
            logger.warning("External OCR not yet implemented - returning placeholder")
            return "OCR text extraction not yet implemented. Please implement external OCR API or multimodal LLM integration."

            # Example implementation:
            # text = await self._call_external_ocr_api(file_path)
            # logger.info(f"External OCR extracted {len(text)} characters from image")
            # return text.strip()

        except Exception as e:
            logger.error(f"Image OCR extraction failed: {e}")
            raise
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image to improve OCR accuracy"""
        try:
            # Resize if image is too small
            width, height = image.size
            if width < 1000 or height < 1000:
                scale_factor = max(1000 / width, 1000 / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to grayscale for better OCR
            image = image.convert('L')
            
            # Enhance contrast
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            return image
        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}")
            return image  # Return original if preprocessing fails
    
    def validate_extracted_text(self, text: str) -> bool:
        """Validate that extracted text looks like an invoice"""
        if not text or len(text.strip()) < 20:
            return False
        
        # Look for common invoice keywords
        invoice_keywords = [
            'invoice', 'faktura', 'bill', 'receipt', 'kvitto',
            'amount', 'belopp', 'total', 'summa', 'date', 'datum',
            'supplier', 'leverantör', 'customer', 'kund'
        ]
        
        text_lower = text.lower()
        keyword_count = sum(1 for keyword in invoice_keywords if keyword in text_lower)
        
        # Should have at least 2 invoice-related keywords
        return keyword_count >= 2


# Global service instance
_ocr_service = None


def get_ocr_service() -> OCRService:
    """Get singleton OCR service instance"""
    global _ocr_service
    if _ocr_service is None:
        _ocr_service = OCRService()
    return _ocr_service
