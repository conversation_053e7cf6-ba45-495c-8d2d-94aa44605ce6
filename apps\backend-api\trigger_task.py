#!/usr/bin/env python3
"""
Manually trigger processing task for pending sessions
"""
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models.invoice import Session
from sqlalchemy import text
from app.tasks.invoice_processing_tasks import process_invoice_task

def trigger_pending_tasks():
    """Manually trigger tasks for pending sessions"""
    
    db = SessionLocal()
    
    try:
        print("🚀 Triggering tasks for pending sessions...")
        
        # Hämta alla sessioner med status pending
        sessions = db.execute(text("SELECT id, invoice_id, tenant_id FROM sessions WHERE status = 'pending' ORDER BY created_at DESC")).fetchall()
        print(f"\n📋 Found {len(sessions)} pending sessions:")
        
        for session in sessions:
            session_id, invoice_id, tenant_id = session
            print(f"  Triggering task for session {session_id}, invoice {invoice_id}")
            
            try:
                # Starta processing task
                task = process_invoice_task.delay(
                    invoice_id=str(invoice_id),
                    tenant_id=str(tenant_id)
                )
                print(f"    ✅ Task {task.id} started successfully")
                
            except Exception as e:
                print(f"    ❌ Failed to start task: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    trigger_pending_tasks()
