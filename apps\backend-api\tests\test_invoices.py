import pytest
import tempfile
import os
from fastapi.testclient import Test<PERSON>lient
from app.models.invoice import Invoice, AccountingEntry


class TestInvoiceAPI:
    """Test invoice API endpoints"""
    
    def test_upload_invoice_success(self, client: TestClient, authenticated_headers, temp_upload_dir, sample_pdf_file):
        """Test successful invoice upload"""
        headers, user, tenant, role = authenticated_headers
        
        with open(sample_pdf_file, 'rb') as f:
            response = client.post(
                "/invoices/upload",
                files={"file": ("test_invoice.pdf", f, "application/pdf")},
                data={"supplier_name": "Test Supplier"},
                headers=headers
            )
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert data["message"] == "Invoice uploaded successfully and AI agent processing started"
        assert data["status"] == "pending"
        assert data["processing_type"] == "ai_agent"
    
    def test_upload_invoice_invalid_file_type(self, client: TestClient, authenticated_headers):
        """Test upload with invalid file type"""
        headers, user, tenant, role = authenticated_headers
        
        # Create a text file
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write(b"This is not a valid invoice file")
            f.flush()
            
            with open(f.name, 'rb') as file:
                response = client.post(
                    "/invoices/upload",
                    files={"file": ("test.txt", file, "text/plain")},
                    headers=headers
                )
        
        os.unlink(f.name)
        
        assert response.status_code == 400
        assert "File type not supported" in response.json()["detail"]
    
    def test_list_invoices(self, client: TestClient, authenticated_headers, db_session, sample_invoice_data):
        """Test listing invoices"""
        headers, user, tenant, role = authenticated_headers
        
        # Create test invoice
        invoice_data = sample_invoice_data.copy()
        invoice_data["tenant_id"] = tenant.id
        invoice = Invoice(**invoice_data)
        db_session.add(invoice)
        db_session.commit()
        
        response = client.get("/invoices/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["supplier_name"] == "Test Supplier"
        assert data[0]["status"] == "pending"
    
    def test_list_invoices_with_status_filter(self, client: TestClient, authenticated_headers, db_session, sample_invoice_data):
        """Test listing invoices with status filter"""
        headers, user, tenant, role = authenticated_headers
        
        # Create test invoices with different statuses
        for status in ["pending", "completed", "failed"]:
            invoice_data = sample_invoice_data.copy()
            invoice_data["tenant_id"] = tenant.id
            invoice_data["status"] = status
            invoice_data["invoice_number"] = f"INV-{status}"
            invoice = Invoice(**invoice_data)
            db_session.add(invoice)
        
        db_session.commit()
        
        # Test filter for completed invoices
        response = client.get("/invoices/?status=completed", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "completed"
    
    def test_get_invoice_details(self, client: TestClient, authenticated_headers, db_session, sample_invoice_data):
        """Test getting invoice details"""
        headers, user, tenant, role = authenticated_headers
        
        # Create test invoice
        invoice_data = sample_invoice_data.copy()
        invoice_data["tenant_id"] = tenant.id
        invoice = Invoice(**invoice_data)
        db_session.add(invoice)
        db_session.commit()
        db_session.refresh(invoice)
        
        # Create accounting entry
        accounting_entry = AccountingEntry(
            tenant_id=tenant.id,
            invoice_id=invoice.id,
            account_code="4010",
            account_name="Office Supplies",
            debit_amount=1500.00,
            confidence_score=0.95,
            is_validated=False
        )
        db_session.add(accounting_entry)
        db_session.commit()
        
        response = client.get(f"/invoices/{invoice.id}", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["supplier_name"] == "Test Supplier"
        assert data["total_amount"] == 1500.00
        assert len(data["accounting_entries"]) == 1
        assert data["accounting_entries"][0]["account_code"] == "4010"
    
    def test_get_invoice_not_found(self, client: TestClient, authenticated_headers):
        """Test getting non-existent invoice"""
        headers, user, tenant, role = authenticated_headers
        
        response = client.get("/invoices/********-0000-0000-0000-************", headers=headers)
        
        assert response.status_code == 404
        assert "Invoice not found" in response.json()["detail"]
    
    def test_validate_invoice(self, client: TestClient, authenticated_headers, db_session, sample_invoice_data):
        """Test validating an invoice"""
        headers, user, tenant, role = authenticated_headers
        
        # Update role permissions to include validation
        role.permissions.append("accounting:validate")
        db_session.commit()
        
        # Create test invoice
        invoice_data = sample_invoice_data.copy()
        invoice_data["tenant_id"] = tenant.id
        invoice_data["status"] = "needs_review"
        invoice = Invoice(**invoice_data)
        db_session.add(invoice)
        db_session.commit()
        db_session.refresh(invoice)
        
        # Create accounting entry
        accounting_entry = AccountingEntry(
            tenant_id=tenant.id,
            invoice_id=invoice.id,
            account_code="4010",
            account_name="Office Supplies",
            debit_amount=1500.00,
            confidence_score=0.95,
            is_validated=False
        )
        db_session.add(accounting_entry)
        db_session.commit()
        
        response = client.put(f"/invoices/{invoice.id}/validate", headers=headers)
        
        assert response.status_code == 200
        assert "Invoice validated successfully" in response.json()["message"]
        
        # Check that invoice status is updated
        db_session.refresh(invoice)
        assert invoice.status == "completed"
        
        # Check that accounting entry is validated
        db_session.refresh(accounting_entry)
        assert accounting_entry.is_validated is True
        assert accounting_entry.validated_by == user.id
    
    def test_validate_invoice_insufficient_permissions(self, client: TestClient, authenticated_headers, db_session, sample_invoice_data):
        """Test validating invoice without proper permissions"""
        headers, user, tenant, role = authenticated_headers
        
        # Create test invoice
        invoice_data = sample_invoice_data.copy()
        invoice_data["tenant_id"] = tenant.id
        invoice = Invoice(**invoice_data)
        db_session.add(invoice)
        db_session.commit()
        db_session.refresh(invoice)
        
        response = client.put(f"/invoices/{invoice.id}/validate", headers=headers)
        
        assert response.status_code == 403
        assert "Permission denied" in response.json()["detail"]
    
    def test_delete_invoice(self, client: TestClient, authenticated_headers, db_session, sample_invoice_data):
        """Test deleting an invoice"""
        headers, user, tenant, role = authenticated_headers
        
        # Update role permissions to include delete
        role.permissions.append("invoices:delete")
        db_session.commit()
        
        # Create test invoice
        invoice_data = sample_invoice_data.copy()
        invoice_data["tenant_id"] = tenant.id
        invoice = Invoice(**invoice_data)
        db_session.add(invoice)
        db_session.commit()
        db_session.refresh(invoice)
        
        response = client.delete(f"/invoices/{invoice.id}", headers=headers)
        
        assert response.status_code == 200
        assert "Invoice deleted successfully" in response.json()["message"]
        
        # Verify invoice is deleted
        deleted_invoice = db_session.query(Invoice).filter(Invoice.id == invoice.id).first()
        assert deleted_invoice is None
    
    def test_tenant_isolation(self, client: TestClient, db_session, sample_user_data, sample_tenant_data, sample_invoice_data):
        """Test that users can only see invoices from their tenant"""
        from app.models.user import User, Role, TenantUser
        from app.models.tenant import Tenant
        from app.utils.security import get_password_hash, create_access_token
        
        # Create two tenants
        tenant1 = Tenant(name="Tenant 1", is_active=True)
        tenant2 = Tenant(name="Tenant 2", is_active=True)
        db_session.add_all([tenant1, tenant2])
        db_session.commit()
        
        # Create role
        role = Role(name="admin", description="Admin", permissions=["invoices:read"])
        db_session.add(role)
        db_session.commit()
        
        # Create user
        user_data = sample_user_data.copy()
        password = user_data.pop("password")
        user_data["hashed_password"] = get_password_hash(password)
        user = User(**user_data)
        db_session.add(user)
        db_session.commit()
        
        # Associate user with tenant1 only
        tenant_user = TenantUser(
            user_id=user.id,
            tenant_id=tenant1.id,
            role_id=role.id,
            is_active=True
        )
        db_session.add(tenant_user)
        db_session.commit()
        
        # Create invoices in both tenants
        invoice1_data = sample_invoice_data.copy()
        invoice1_data["tenant_id"] = tenant1.id
        invoice1_data["supplier_name"] = "Supplier 1"
        invoice1 = Invoice(**invoice1_data)
        
        invoice2_data = sample_invoice_data.copy()
        invoice2_data["tenant_id"] = tenant2.id
        invoice2_data["supplier_name"] = "Supplier 2"
        invoice2 = Invoice(**invoice2_data)
        
        db_session.add_all([invoice1, invoice2])
        db_session.commit()
        
        # Create access token and headers for tenant1
        access_token = create_access_token({"sub": str(user.id)})
        headers = {
            "Authorization": f"Bearer {access_token}",
            "X-Tenant-ID": str(tenant1.id)
        }
        
        # User should only see invoice from tenant1
        response = client.get("/invoices/", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["supplier_name"] == "Supplier 1"
