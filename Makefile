.PHONY: help dev dev-up dev-down dev-logs dev-restart dev-build dev-shell prod stop clean test migrate shell

help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Development commands with hot reload
dev: dev-up ## Start development environment with hot reload (alias for dev-up)

dev-up: ## Start development environment with hot reload
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

dev-up-d: ## Start development environment with hot reload in background
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build

dev-down: ## Stop development environment
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

dev-logs: ## Show development logs (follow)
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f

dev-logs-backend: ## Show backend logs only
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f backend-api worker scheduler

dev-logs-frontend: ## Show frontend logs only
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f admin-frontend app-frontend

dev-restart: ## Restart all development services
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart

dev-restart-backend: ## Restart backend services only
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart backend-api worker scheduler

dev-restart-frontend: ## Restart frontend services only
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart admin-frontend app-frontend

dev-build: ## Rebuild development images
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml build

dev-shell: ## Open shell in development API container
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend-api bash

dev-shell-admin: ## Open shell in admin frontend container
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec admin-frontend sh

dev-shell-app: ## Open shell in app frontend container
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec app-frontend sh

# Production commands
prod: ## Start production environment
	docker-compose -f docker-compose.prod.yml up -d

# General commands
stop: ## Stop all services (dev and prod)
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
	docker-compose down
	docker-compose -f docker-compose.prod.yml down

clean: ## Clean up containers and volumes
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v
	docker-compose down -v
	docker-compose -f docker-compose.prod.yml down -v
	docker system prune -f

test: ## Run tests in development environment
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend-api pytest
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec admin-frontend npm test -- --watchAll=false
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec app-frontend npm test -- --watchAll=false

migrate: ## Run database migrations
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend-api alembic upgrade head

shell: ## Open shell in API container (legacy command)
	docker-compose exec backend-api bash

logs: ## Show logs (legacy command)
	docker-compose logs -f

build: ## Build all images
	docker-compose build
	docker-compose -f docker-compose.prod.yml build

setup: ## Initial setup
	cp .env.example .env
	@echo "Please edit .env file with your configuration"
	@echo "Then run: make dev"
