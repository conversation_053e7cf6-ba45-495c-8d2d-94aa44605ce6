#!/usr/bin/env python3
"""
Debug script to check session status and Celery queue
"""
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models.invoice import Session
from sqlalchemy import text
import redis

def debug_sessions():
    """Check session status and Celery queue"""
    
    db = SessionLocal()
    
    try:
        print("🔍 Debugging sessions and Celery queue...")
        
        # Hämta alla sessioner med status pending
        sessions = db.execute(text("SELECT id, invoice_id, status, created_at FROM sessions WHERE status = 'pending' ORDER BY created_at DESC LIMIT 10")).fetchall()
        print(f"\n📋 Found {len(sessions)} pending sessions:")
        for session in sessions:
            print(f"  Session {session[0]}: invoice_id={session[1]}, status={session[2]}, created={session[3]}")
        
        # Kontrollera om det finns några tasks i Redis
        try:
            r = redis.Redis(host='redis', port=6379, db=0)
            queue_length = r.llen('celery')
            print(f"\n📊 Celery queue length: {queue_length}")
            
            # Kontrollera Redis-anslutning
            r.ping()
            print("✅ Redis connection: OK")
            
        except Exception as e:
            print(f"❌ Redis connection error: {e}")
        
        # Kontrollera alla sessioner (inte bara pending)
        all_sessions = db.execute(text("SELECT status, COUNT(*) FROM sessions GROUP BY status")).fetchall()
        print(f"\n📈 Session status summary:")
        for status, count in all_sessions:
            print(f"  {status}: {count}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    debug_sessions()
