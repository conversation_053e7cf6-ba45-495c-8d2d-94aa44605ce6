"""
Abstract base class for invoice fetching integrations.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class IntegrationType(str, Enum):
    """Supported integration types"""
    FORTNOX = "FORTNOX"
    VISMA = "VISMA"
    HTTP = "HTTP"


class InvoiceFetcher(ABC):
    """
    Abstract base class for invoice fetching from different systems.
    
    Each integration must implement the core methods for fetching invoices
    and handling post-processing callbacks.
    """
    
    def __init__(self, tenant_id: str, configuration: Dict[str, Any]):
        """
        Initialize the invoice fetcher.
        
        Args:
            tenant_id: The tenant ID this fetcher belongs to
            configuration: Integration-specific configuration
        """
        self.tenant_id = tenant_id
        self.configuration = configuration
        self.logger = logging.getLogger(f"{self.__class__.__name__}.{tenant_id}")
    
    @abstractmethod
    async def fetch_invoices(self, since: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch new invoices from the integration source.
        
        Args:
            since: Optional timestamp to fetch invoices since (ISO format)
            
        Returns:
            List of invoice dictionaries with standardized format:
            {
                "external_id": str,
                "invoice_number": str,
                "supplier_name": str,
                "invoice_date": str,
                "due_date": str,
                "total_amount": float,
                "currency": str,
                "file_url": str,
                "file_type": str,
                "metadata": Dict[str, Any]
            }
        """
        pass
    
    @abstractmethod
    async def post_processing_callback(self, invoice_id: str, status: str, data: Dict[str, Any] = None) -> bool:
        """
        Handle post-processing callback after invoice is processed.
        
        Args:
            invoice_id: The external invoice ID
            status: Processing status (completed, failed, needs_review)
            data: Optional additional data about the processing
            
        Returns:
            True if callback was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to the integration source.
        
        Returns:
            Dictionary with connection test results:
            {
                "success": bool,
                "message": str,
                "details": Dict[str, Any]
            }
        """
        pass
    
    def _standardize_invoice_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standardize raw invoice data to common format.
        Override in subclasses for integration-specific transformations.
        
        Args:
            raw_data: Raw invoice data from the integration
            
        Returns:
            Standardized invoice data
        """
        return {
            "external_id": raw_data.get("id", ""),
            "invoice_number": raw_data.get("invoice_number", ""),
            "supplier_name": raw_data.get("supplier_name", ""),
            "invoice_date": raw_data.get("invoice_date", ""),
            "due_date": raw_data.get("due_date", ""),
            "total_amount": raw_data.get("total_amount", 0.0),
            "currency": raw_data.get("currency", "SEK"),
            "file_url": raw_data.get("file_url", ""),
            "file_type": raw_data.get("file_type", "pdf"),
            "metadata": raw_data.get("metadata", {})
        }
    
    async def _handle_rate_limit(self, retry_after: int = 60):
        """
        Handle rate limiting by waiting for the specified time.
        
        Args:
            retry_after: Seconds to wait before retrying
        """
        import asyncio
        self.logger.warning(f"Rate limited, waiting {retry_after} seconds")
        await asyncio.sleep(retry_after)
    
    def _log_error(self, operation: str, error: Exception, context: Dict[str, Any] = None):
        """
        Log errors with consistent formatting.
        
        Args:
            operation: The operation that failed
            error: The exception that occurred
            context: Additional context information
        """
        context_str = f" Context: {context}" if context else ""
        self.logger.error(f"Error in {operation}: {str(error)}{context_str}", exc_info=True)
