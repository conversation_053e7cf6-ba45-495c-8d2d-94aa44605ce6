{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@aggie/ui-components": ["packages/ui-components/src/index.ts"], "@aggie/shared-types": ["packages/shared-types/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}