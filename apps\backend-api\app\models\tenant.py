from sqlalchemy import Column, String, Boolean, Text, Enum
from sqlalchemy.orm import relationship
from .base import BaseModel
import enum


class TenantType(enum.Enum):
    """Enum for tenant types"""
    STANDARD = "standard"
    AGENCY = "agency"


class Tenant(BaseModel):
    """Tenant model for multi-tenancy"""
    __tablename__ = "tenants"

    name = Column(String(255), nullable=False)
    domain = Column(String(255), unique=True, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    settings = Column(Text, nullable=True)  # JSON settings
    company_context = Column(Text, nullable=True)  # Company information/context
    tenant_type = Column(Enum(TenantType, name='tenant_type_enum', values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=TenantType.STANDARD)
    
    # Relationships
    tenant_users = relationship("TenantUser", back_populates="tenant", cascade="all, delete-orphan")
    invoices = relationship("Invoice", back_populates="tenant", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="tenant", cascade="all, delete-orphan")
    invoice_integrations = relationship("InvoiceIntegration", back_populates="tenant", cascade="all, delete-orphan")
    sessions = relationship("Session", back_populates="tenant", cascade="all, delete-orphan", overlaps="tenant")
    session_logs = relationship("SessionLog", back_populates="tenant", cascade="all, delete-orphan", overlaps="tenant")

    def __repr__(self):
        return f"<Tenant(id={self.id}, name='{self.name}')>"
