# Google Search API Setup Guide

## Overview

The AI Agent system uses Google Custom Search API to enrich invoice context by searching for supplier and product information. This is optional but recommended for better accuracy.

## Setup Steps

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable billing for the project (required for API usage)

### 2. Enable Custom Search API

1. Go to [Google Cloud Console APIs & Services](https://console.cloud.google.com/apis/dashboard)
2. Click "Enable APIs and Services"
3. Search for "Custom Search API"
4. Click on "Custom Search API" and enable it

### 3. Create API Key

1. Go to [Credentials page](https://console.cloud.google.com/apis/credentials)
2. Click "Create Credentials" → "API Key"
3. Copy the API key
4. (Optional) Restrict the API key to Custom Search API only for security

### 4. Create Custom Search Engine

1. Go to [Google Custom Search Engine](https://cse.google.com/cse/)
2. Click "Add" to create a new search engine
3. Configure the search engine:
   - **Sites to search**: Enter `*` to search the entire web
   - **Name**: "AI Agent Invoice Search" (or any name)
   - **Language**: Select your preferred language
4. Click "Create"
5. Copy the "Search engine ID" (cx parameter)

### 5. Configure Environment Variables

Add the following to your `.env` file:

```bash
# Google Search API Configuration
GOOGLE_SEARCH_API_KEY=your_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
```

### 6. Test Configuration

Create a test script to verify the setup:

```python
import requests
import os

api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')

if api_key and engine_id:
    url = "https://www.googleapis.com/customsearch/v1"
    params = {
        'key': api_key,
        'cx': engine_id,
        'q': 'ACME Corporation company business',
        'num': 3
    }
    
    response = requests.get(url, params=params)
    if response.status_code == 200:
        print("✅ Google Search API configured correctly!")
        data = response.json()
        print(f"Found {len(data.get('items', []))} results")
    else:
        print(f"❌ API test failed: {response.status_code}")
        print(response.text)
else:
    print("❌ API key or engine ID not configured")
```

## Usage Limits and Pricing

### Free Tier
- 100 search queries per day
- No cost

### Paid Usage
- $5 per 1,000 queries (after free tier)
- Up to 10,000 queries per day

### Rate Limits
- 10 queries per second per IP address
- 100 queries per 100 seconds per user

## Configuration in AI Agent

The AI Agent system will automatically use Google Search if configured:

1. **WebSearchTool** checks for API credentials
2. If available, performs real searches
3. If not available, returns mock data with a note

### Search Behavior

- **Supplier Search**: Searches for company information, industry, website
- **Product Search**: Searches for product categories and descriptions
- **Rate Limiting**: Built-in delays to respect API limits
- **Error Handling**: Graceful fallback to mock data on errors

## Troubleshooting

### Common Issues

1. **"API key not valid"**
   - Verify API key is correct
   - Check that Custom Search API is enabled
   - Ensure billing is enabled on the project

2. **"Invalid search engine ID"**
   - Verify the search engine ID (cx parameter)
   - Ensure the search engine is set to search the entire web

3. **"Quota exceeded"**
   - Check daily usage in Google Cloud Console
   - Consider upgrading to paid tier
   - Implement caching to reduce API calls

4. **"Access forbidden"**
   - Check API key restrictions
   - Verify the API key has access to Custom Search API

### Testing Commands

```bash
# Test API key
curl "https://www.googleapis.com/customsearch/v1?key=YOUR_API_KEY&cx=YOUR_ENGINE_ID&q=test"

# Check quota usage
# Go to Google Cloud Console → APIs & Services → Quotas
```

## Alternative Configuration

If you don't want to use Google Search API, the system will work with mock data:

1. Leave `GOOGLE_SEARCH_API_KEY` and `GOOGLE_SEARCH_ENGINE_ID` unset
2. The WebSearchTool will return placeholder data
3. The AI agent will still function but with less context enrichment

## Security Best Practices

1. **Restrict API Key**: Limit to Custom Search API only
2. **Environment Variables**: Never commit API keys to version control
3. **IP Restrictions**: Consider restricting API key to your server IPs
4. **Monitoring**: Set up alerts for unusual API usage
5. **Rotation**: Regularly rotate API keys

## Cost Optimization

1. **Caching**: Cache search results to avoid duplicate queries
2. **Selective Search**: Only search for unknown suppliers/products
3. **Batch Processing**: Group similar searches together
4. **Monitoring**: Track usage to stay within budget

## Integration Status

Once configured, you can verify the integration:

1. Check the AI Agent logs for search activity
2. Monitor the thought chains for search results
3. Review tool results in session details
4. Check Google Cloud Console for API usage

The system will log whether real searches or mock data is being used.
