{"name": "@aggie/ui-components", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "echo 'UI components built successfully'"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "~5.5.2"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}