"""
Schemas för det nya fakturabehandlingssystemet
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from enum import Enum


class ImportType(str, Enum):
    """Import typer för fakturor"""
    MANUAL = "manuell"
    VISMA = "Visma eEkonomi"
    FORTNOX = "Fortnox"


class SessionStatus(str, Enum):
    """Session status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProcessingStep(str, Enum):
    """Bearbetningssteg"""
    EXTRAHERA = "extrahera"
    KONTEXT = "kontext"
    HITTA_KONTO = "hitta_konto"
    BOKFORA = "bokfora"


# Request schemas
class InvoiceCreateRequest(BaseModel):
    """Request för att skapa en ny faktura"""
    import_typ: ImportType
    file_data: str = Field(..., description="Faktisk data fil i base64 eller annat lämpligt format")
    metadata_ERP: Optional[Dict[str, Any]] = Field(None, description="Data objekt från ERP (tomt om manuellt)")
    original_filename: Optional[str] = None
    file_type: Optional[str] = None


class SessionRetryRequest(BaseModel):
    """Request för att återförsöka session bearbetning"""
    from_step: Optional[ProcessingStep] = Field(None, description="Steg att börja från")


class StepExecuteRequest(BaseModel):
    """Request för att köra ett specifikt steg"""
    step_name: ProcessingStep


# Response schemas
class InvoiceResponse(BaseModel):
    """Response för faktura"""
    id: UUID
    tenant_id: UUID
    import_typ: str
    metadata_ERP: Optional[Dict[str, Any]]
    supplier_name: Optional[str]
    invoice_number: Optional[str]
    invoice_date: Optional[str]
    due_date: Optional[str]
    total_amount: Optional[float]
    currency: Optional[str]
    raw_data: Optional[Dict[str, Any]]
    original_filename: Optional[str]
    file_type: Optional[str]
    status: str
    processing_error: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SessionLogResponse(BaseModel):
    """Response för session log"""
    id: UUID
    step_name: str
    prompt_sent: str
    llm_response: str
    reasoning: Optional[str]
    execution_time_ms: Optional[float]
    success: bool
    error_message: Optional[str]
    input_tokens: Optional[int]
    output_tokens: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True


class ProcessingResultsResponse(BaseModel):
    """Response för bearbetningsresultat"""
    extracted_data: Optional[str]
    extracted_reasoning: Optional[str]
    context_data: Optional[str]
    context_reasoning: Optional[str]
    account_data: Optional[Union[Dict[str, Any], List[Any]]]
    account_reasoning: Optional[str]
    booking_result: Optional[Union[Dict[str, Any], List[Any]]]
    booking_reasoning: Optional[str]


class SessionStatisticsResponse(BaseModel):
    """Response för session statistik"""
    total_steps: int
    successful_steps: int
    failed_steps: int
    total_execution_time_ms: float
    average_execution_time_ms: float
    steps_completed: List[str]
    total_input_tokens: int
    total_output_tokens: int
    total_tokens: int


class SessionResponse(BaseModel):
    """Response för session"""
    id: UUID
    invoice_id: UUID
    status: str
    current_step: Optional[str]
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str]
    failed_step: Optional[str]

    class Config:
        from_attributes = True


class SessionDetailResponse(BaseModel):
    """Detaljerad response för session"""
    session: SessionResponse
    invoice: Optional[InvoiceResponse]
    processing_results: ProcessingResultsResponse
    logs: List[SessionLogResponse]
    statistics: SessionStatisticsResponse


class SessionSummaryItem(BaseModel):
    """Sammanfattning av en session"""
    id: UUID
    invoice_id: UUID
    status: str
    current_step: Optional[str]
    created_at: datetime
    updated_at: datetime
    error_message: Optional[str]
    failed_step: Optional[str]
    invoice: Optional[Dict[str, Any]]
    log_count: int


class PaginationResponse(BaseModel):
    """Pagination information"""
    total: int
    limit: int
    offset: int
    has_more: bool


class TenantStatisticsResponse(BaseModel):
    """Tenant statistik"""
    sessions_by_status: Dict[str, int]
    total_sessions: int
    total_logs: int
    successful_logs: int
    failed_logs: int
    success_rate: float


class SessionsSummaryResponse(BaseModel):
    """Response för sessions sammanfattning"""
    sessions: List[SessionSummaryItem]
    pagination: PaginationResponse
    statistics: TenantStatisticsResponse


class ProcessingTaskResponse(BaseModel):
    """Response för bearbetningsuppgift"""
    task_id: str
    session_id: UUID
    status: str
    message: str


class LLMProviderInfoResponse(BaseModel):
    """Response för LLM provider information"""
    provider: str
    status: str


class PromptListResponse(BaseModel):
    """Response för tillgängliga prompts"""
    prompts: Dict[str, str]


class StepExecutionResponse(BaseModel):
    """Response för steg exekvering"""
    session_id: UUID
    step_completed: str
    status: str
    execution_time_ms: Optional[float]
    success: bool
    error_message: Optional[str]
