"""Add session_id to action_items

Revision ID: 015
Revises: 014
Create Date: 2025-01-06 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '015'
down_revision = '014'
branch_labels = None
depends_on = None


def upgrade():
    # Add session_id column to action_items table
    op.add_column('action_items', sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_action_items_session_id',
        'action_items', 
        'sessions',
        ['session_id'], 
        ['id']
    )


def downgrade():
    # Drop foreign key constraint
    op.drop_constraint('fk_action_items_session_id', 'action_items', type_='foreignkey')
    
    # Drop session_id column
    op.drop_column('action_items', 'session_id')
