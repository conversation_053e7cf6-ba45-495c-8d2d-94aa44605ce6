#!/usr/bin/env python3
"""
Test script to verify database session recovery functionality
"""
import sys
import os
import asyncio
from uuid import uuid4

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal, set_tenant_context, recover_database_session, safe_commit
from app.models.invoice import Invoice, Session as ProcessingSession
from app.services.invoice_processing_service import InvoiceProcessingService
from sqlalchemy.exc import PendingRollbackError
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_session_recovery():
    """Test database session recovery after rollback"""

    db = SessionLocal()

    try:
        # Use existing tenant
        test_tenant_id = "94311644-a034-4967-87ea-bc46068ab6be"
        set_tenant_context(db, test_tenant_id)
        
        logger.info("🧪 Testing database session recovery...")
        
        # Create a test invoice
        test_invoice = Invoice(
            tenant_id=test_tenant_id,
            import_typ="test",
            file_data="test_data",
            supplier_name="Test Supplier",
            status="pending"
        )
        
        db.add(test_invoice)
        db.commit()
        db.refresh(test_invoice)
        
        logger.info(f"✅ Created test invoice: {test_invoice.id}")
        
        # Create a test session
        test_session = ProcessingSession(
            tenant_id=test_tenant_id,
            invoice_id=test_invoice.id,
            status="pending"
        )
        
        db.add(test_session)
        db.commit()
        db.refresh(test_session)
        
        logger.info(f"✅ Created test session: {test_session.id}")
        
        # Simulate a database error by forcing a rollback
        try:
            # This will cause a rollback
            db.execute("SELECT * FROM non_existent_table")
        except Exception as e:
            logger.info(f"🔄 Simulated database error: {e}")
        
        # Try to use the session - this should trigger PendingRollbackError
        try:
            test_session.status = "processing"
            db.commit()
            logger.error("❌ Expected PendingRollbackError but didn't get one")
        except PendingRollbackError as e:
            logger.info(f"✅ Got expected PendingRollbackError: {e}")
        
        # Now test our recovery function
        logger.info("🔧 Testing session recovery...")
        recover_database_session(db)
        
        # Refresh the objects and try again
        db.refresh(test_session)
        test_session.status = "processing"
        
        if safe_commit(db, "session status update"):
            logger.info("✅ Session recovery successful!")
        else:
            logger.error("❌ Session recovery failed!")
        
        # Clean up
        db.delete(test_session)
        db.delete(test_invoice)
        db.commit()
        
        logger.info("🧹 Cleaned up test data")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise
    finally:
        db.close()

async def test_invoice_processing_error_handling():
    """Test invoice processing service error handling"""

    db = SessionLocal()

    try:
        # Use existing tenant
        test_tenant_id = "94311644-a034-4967-87ea-bc46068ab6be"
        set_tenant_context(db, test_tenant_id)
        
        logger.info("🧪 Testing invoice processing error handling...")
        
        # Create a test invoice with invalid data to trigger an error
        test_invoice = Invoice(
            tenant_id=test_tenant_id,
            import_typ="test",
            file_data="invalid_base64_data",  # This should cause processing to fail
            supplier_name="Test Supplier",
            status="pending"
        )
        
        db.add(test_invoice)
        db.commit()
        db.refresh(test_invoice)
        
        logger.info(f"✅ Created test invoice: {test_invoice.id}")
        
        # Create processing service
        processing_service = InvoiceProcessingService(db)
        
        # Try to process the invoice - this should fail gracefully
        try:
            result = await processing_service.process_invoice(
                invoice_id=test_invoice.id,
                tenant_id=test_tenant_id
            )
            logger.error("❌ Expected processing to fail but it succeeded")
        except Exception as e:
            logger.info(f"✅ Processing failed as expected: {e}")
        
        # Check that the invoice status was updated properly
        db.refresh(test_invoice)
        if test_invoice.status == "failed":
            logger.info("✅ Invoice status correctly updated to failed")
        else:
            logger.error(f"❌ Invoice status is {test_invoice.status}, expected 'failed'")
        
        # Clean up
        # Find and delete any sessions
        sessions = db.query(ProcessingSession).filter(
            ProcessingSession.invoice_id == test_invoice.id
        ).all()
        for session in sessions:
            db.delete(session)
        
        db.delete(test_invoice)
        db.commit()
        
        logger.info("🧹 Cleaned up test data")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Starting database session recovery tests...")
    
    # Test 1: Basic session recovery
    test_session_recovery()
    
    # Test 2: Invoice processing error handling
    asyncio.run(test_invoice_processing_error_handling())
    
    print("✅ All tests completed!")
