"""
Fortnox invoice integration implementation.
"""

import httpx
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from urllib.parse import urljoin

from .base import InvoiceFetcher
from app.config import settings
from app.utils.oauth2 import OAuth2<PERSON>anager
from app.models.integration import OAuth2<PERSON>oken
from sqlalchemy.orm import Session


class FortnoxInvoiceFetcher(InvoiceFetcher):
    """
    Fortnox invoice fetcher implementation.
    
    Handles OAuth2 authentication and fetches invoices from Fortnox Inbox API.
    """
    
    def __init__(self, tenant_id: str, configuration: Dict[str, Any], db: Session):
        super().__init__(tenant_id, configuration)
        self.db = db
        self.oauth_manager = OAuth2Manager(db)
        self.base_url = settings.fortnox_api_url
        self.integration_id = configuration.get("integration_id")
        
    async def _get_authenticated_client(self) -> httpx.AsyncClient:
        """Get HTTP client with valid OAuth2 token"""
        if not self.integration_id:
            raise ValueError("Integration ID not found in configuration")
            
        token = self.oauth_manager.get_valid_token(self.integration_id)
        if not token:
            raise ValueError("No valid OAuth2 token found for Fortnox integration")
        
        # Refresh token if needed
        if token.is_expired:
            token = await self.oauth_manager.refresh_token_if_needed(token)
        
        headers = {
            "Authorization": f"Bearer {token.access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        return httpx.AsyncClient(headers=headers, timeout=30.0)
    
    async def fetch_invoices(self, since: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fetch invoices from Fortnox Inbox API.
        
        Args:
            since: ISO timestamp to fetch invoices since
            
        Returns:
            List of standardized invoice data
        """
        try:
            async with await self._get_authenticated_client() as client:
                # Fortnox Inbox API endpoint
                url = urljoin(self.base_url, "inbox")
                
                params = {}
                if since:
                    # Convert to Fortnox date format if needed
                    params["lastmodified"] = since
                
                self.logger.info(f"Fetching invoices from Fortnox Inbox API: {url}")
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                inbox_items = data.get("Inbox", [])
                
                invoices = []
                for item in inbox_items:
                    try:
                        invoice_data = await self._process_inbox_item(client, item)
                        if invoice_data:
                            invoices.append(invoice_data)
                    except Exception as e:
                        self.logger.error(f"Error processing inbox item {item.get('Id', 'unknown')}: {e}")
                        continue
                
                self.logger.info(f"Successfully fetched {len(invoices)} invoices from Fortnox")
                return invoices
                
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                await self._handle_rate_limit(60)
                raise
            elif e.response.status_code == 401:
                self.logger.error("Fortnox authentication failed - token may be invalid")
                raise ValueError("Authentication failed")
            else:
                self.logger.error(f"Fortnox API error: {e.response.status_code} - {e.response.text}")
                raise
        except Exception as e:
            self._log_error("fetch_invoices", e, {"since": since})
            raise
    
    async def _process_inbox_item(self, client: httpx.AsyncClient, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process a single inbox item and extract invoice data.
        
        Args:
            client: Authenticated HTTP client
            item: Inbox item from Fortnox API
            
        Returns:
            Standardized invoice data or None if not processable
        """
        try:
            item_id = item.get("Id")
            if not item_id:
                return None
            
            # Get detailed item information
            detail_url = urljoin(self.base_url, f"inbox/{item_id}")
            detail_response = await client.get(detail_url)
            detail_response.raise_for_status()
            
            detail_data = detail_response.json()
            inbox_detail = detail_data.get("Inbox", {})
            
            # Extract file URL for download
            file_url = None
            if "Path" in inbox_detail:
                file_url = urljoin(self.base_url, f"inbox/{item_id}/file")
            
            # Extract invoice metadata
            invoice_data = {
                "external_id": str(item_id),
                "invoice_number": inbox_detail.get("Name", f"fortnox_{item_id}"),
                "supplier_name": inbox_detail.get("Supplier", "Unknown Supplier"),
                "invoice_date": inbox_detail.get("Date"),
                "due_date": None,  # Not available in inbox
                "total_amount": None,  # Not available in inbox
                "currency": "SEK",  # Default for Fortnox
                "file_url": file_url,
                "file_type": self._determine_file_type(inbox_detail.get("Name", "")),
                "metadata": {
                    "fortnox_id": item_id,
                    "fortnox_path": inbox_detail.get("Path"),
                    "fortnox_size": inbox_detail.get("Size"),
                    "fortnox_created": inbox_detail.get("Created"),
                    "fortnox_supplier": inbox_detail.get("Supplier")
                }
            }
            
            return self._standardize_invoice_data(invoice_data)
            
        except Exception as e:
            self.logger.error(f"Error processing Fortnox inbox item {item.get('Id', 'unknown')}: {e}")
            return None
    
    def _determine_file_type(self, filename: str) -> str:
        """Determine file type from filename"""
        if not filename:
            return "pdf"
        
        extension = filename.lower().split(".")[-1] if "." in filename else ""
        if extension in ["pdf", "png", "jpg", "jpeg"]:
            return extension
        return "pdf"
    
    async def post_processing_callback(self, invoice_id: str, status: str, data: Dict[str, Any] = None) -> bool:
        """
        Send processing status back to Fortnox.
        
        Args:
            invoice_id: External invoice ID (Fortnox inbox item ID)
            status: Processing status
            data: Additional processing data
            
        Returns:
            True if callback was successful
        """
        try:
            async with await self._get_authenticated_client() as client:
                # Fortnox doesn't have a standard callback API for inbox items
                # We could potentially move the item or add metadata
                # For now, just log the status
                
                self.logger.info(f"Fortnox invoice {invoice_id} processing completed with status: {status}")
                
                # If processing was successful, we might want to archive the inbox item
                if status == "completed" and data:
                    await self._archive_inbox_item(client, invoice_id)
                
                return True
                
        except Exception as e:
            self._log_error("post_processing_callback", e, {
                "invoice_id": invoice_id,
                "status": status
            })
            return False
    
    async def _archive_inbox_item(self, client: httpx.AsyncClient, item_id: str) -> bool:
        """
        Archive processed inbox item.
        
        Args:
            client: Authenticated HTTP client
            item_id: Fortnox inbox item ID
            
        Returns:
            True if archiving was successful
        """
        try:
            # Fortnox API doesn't have a direct archive endpoint
            # This is a placeholder for future implementation
            self.logger.info(f"Would archive Fortnox inbox item {item_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error archiving Fortnox inbox item {item_id}: {e}")
            return False
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        Test connection to Fortnox API.
        
        Returns:
            Connection test results
        """
        try:
            async with await self._get_authenticated_client() as client:
                # Test with a simple API call
                url = urljoin(self.base_url, "companyinformation")
                response = await client.get(url)
                response.raise_for_status()
                
                data = response.json()
                company_info = data.get("CompanyInformation", {})
                
                return {
                    "success": True,
                    "message": "Successfully connected to Fortnox API",
                    "details": {
                        "company_name": company_info.get("CompanyName"),
                        "organization_number": company_info.get("OrganizationNumber"),
                        "api_version": "3",
                        "tested_at": datetime.utcnow().isoformat()
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"Failed to connect to Fortnox API: {str(e)}",
                "details": {
                    "error_type": type(e).__name__,
                    "tested_at": datetime.utcnow().isoformat()
                }
            }
