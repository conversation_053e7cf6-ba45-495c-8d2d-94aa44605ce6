from pydantic import BaseModel
from typing import List, Optional
from uuid import UUID


class InvoiceLineAccount(BaseModel):
    """Schema för att sätta konto på en fakturarad"""
    line_description: str
    account_code: str
    account_name: str
    amount: Optional[float] = None


class ActionItemResolution(BaseModel):
    """Schema för att lösa en action item med kontoval"""
    line_accounts: List[InvoiceLineAccount]
    resolution_notes: Optional[str] = None


class ActionItemResolutionResponse(BaseModel):
    """Response för action item resolution"""
    message: str
    session_id: Optional[UUID] = None
    processing_continued: bool = False
