"""
Models for invoice integrations and OAuth tokens.
"""

import pickle
from sqlalchemy import Column, String, <PERSON>ole<PERSON>, ForeignKey, JSON, DateTime, Text, TypeDecorator
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from cryptography.fernet import Fernet
from datetime import datetime

from .base import TenantBaseModel, BaseModel
from app.config import get_encryption_key


class Encrypted(TypeDecorator):
    """
    Custom SQLAlchemy type for encrypted fields using Fernet encryption.

    Based on <PERSON>'s implementation:
    https://blog.miguelgrinberg.com/post/encryption-at-rest-with-sqlalchemy
    """
    impl = Text
    cache_ok = True

    def __init__(self, encryption_key: bytes = None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if encryption_key is None:
            encryption_key = get_encryption_key()
        self.encryption_key = encryption_key
        self.fernet = Fernet(encryption_key)

    def process_bind_param(self, value, dialect):
        """Encrypt value before storing in database"""
        if value is not None:
            # Pickle the value to support any Python data type, then encrypt
            pickled_value = pickle.dumps(value)
            encrypted_value = self.fernet.encrypt(pickled_value)
            # Return as string for Text storage
            return encrypted_value.decode('utf-8')
        return value

    def process_result_value(self, value, dialect):
        """Decrypt value after retrieving from database"""
        if value is not None:
            # Decode from string, decrypt, then unpickle
            encrypted_bytes = value.encode('utf-8')
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return pickle.loads(decrypted_bytes)
        return value


class InvoiceIntegration(TenantBaseModel):
    """Model for storing invoice integration configurations per tenant"""
    __tablename__ = "invoice_integrations"

    integration_type = Column(String(50), nullable=False)  # FORTNOX, VISMA, HTTP
    configuration = Column(Encrypted(), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    name = Column(String(255), nullable=True)  # User-friendly name
    description = Column(String(500), nullable=True)
    
    # Status tracking
    last_sync_at = Column(DateTime, nullable=True)
    last_sync_status = Column(String(50), nullable=True)  # success, failed, partial
    last_error = Column(String(1000), nullable=True)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="invoice_integrations")
    oauth_tokens = relationship("OAuth2Token", back_populates="integration", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<InvoiceIntegration(id={self.id}, tenant_id={self.tenant_id}, type='{self.integration_type}')>"


class OAuth2Token(BaseModel):
    """Model for storing OAuth2 tokens with encryption"""
    __tablename__ = "oauth2_tokens"

    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    integration_id = Column(UUID(as_uuid=True), ForeignKey("invoice_integrations.id"), nullable=False)
    provider = Column(String(50), nullable=False)  # FORTNOX, VISMA
    
    # Encrypted token data
    access_token = Column(Encrypted(), nullable=False)
    refresh_token = Column(Encrypted(), nullable=True)
    token_type = Column(String(50), nullable=False, default="Bearer")
    
    # Token metadata
    expires_at = Column(DateTime, nullable=True)
    scope = Column(String(500), nullable=True)
    
    # Additional provider-specific data (encrypted)
    extra_data = Column(Encrypted(), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    tenant = relationship("Tenant")
    integration = relationship("InvoiceIntegration", back_populates="oauth_tokens")

    def __repr__(self):
        return f"<OAuth2Token(id={self.id}, tenant_id={self.tenant_id}, provider='{self.provider}')>"
    
    @property
    def is_expired(self) -> bool:
        """Check if the token is expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() >= self.expires_at
    
    def to_dict(self) -> dict:
        """Convert token to dictionary for OAuth client usage"""
        return {
            "access_token": self.access_token,
            "refresh_token": self.refresh_token,
            "token_type": self.token_type,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "scope": self.scope
        }
