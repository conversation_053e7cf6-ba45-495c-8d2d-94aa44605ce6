"""Fix cascade delete for action_items

Revision ID: 018
Revises: 017
Create Date: 2025-08-07 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '018'
down_revision = '017'
branch_labels = None
depends_on = None


def upgrade():
    # Drop existing foreign key constraint for session_logs
    try:
        op.drop_constraint('session_logs_session_id_fkey', 'session_logs', type_='foreignkey')
    except:
        # Constraint might not exist or have different name, continue
        pass

    # Add new foreign key constraint with CASCADE delete for session_logs
    op.create_foreign_key(
        'session_logs_session_id_fkey',
        'session_logs',
        'sessions',
        ['session_id'],
        ['id'],
        ondelete='CASCADE'
    )

    # Fix action_items constraints
    # Ensure the old session_id constraint is removed (in case it still exists)
    try:
        op.drop_constraint('fk_action_items_session_id', 'action_items', type_='foreignkey')
    except:
        # Constraint might not exist, continue
        pass

    # Fix invoice_id constraint to CASCADE delete
    try:
        op.drop_constraint('action_items_invoice_id_fkey', 'action_items', type_='foreignkey')
    except:
        # Constraint might not exist, continue
        pass

    # Add new foreign key constraint with CASCADE delete for invoice_id
    op.create_foreign_key(
        'action_items_invoice_id_fkey',
        'action_items',
        'invoices',
        ['invoice_id'],
        ['id'],
        ondelete='CASCADE'
    )


def downgrade():
    # Drop CASCADE foreign key constraint for session_logs
    op.drop_constraint('session_logs_session_id_fkey', 'session_logs', type_='foreignkey')

    # Add back original foreign key constraint without CASCADE for session_logs
    op.create_foreign_key(
        'session_logs_session_id_fkey',
        'session_logs',
        'sessions',
        ['session_id'],
        ['id']
    )

    # Drop CASCADE foreign key constraint for action_items invoice_id
    op.drop_constraint('action_items_invoice_id_fkey', 'action_items', type_='foreignkey')

    # Add back original foreign key constraint without CASCADE for invoice_id
    op.create_foreign_key(
        'action_items_invoice_id_fkey',
        'action_items',
        'invoices',
        ['invoice_id'],
        ['id']
    )
