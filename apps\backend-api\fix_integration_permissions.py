#!/usr/bin/env python3
"""
Fix integration permissions by adding MANAGE_INTEGRATIONS to existing roles
"""
import sys
import os
import json
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import settings

def fix_integration_permissions():
    """Add MANAGE_INTEGRATIONS permission to admin and manager roles"""
    
    # Create database connection
    engine = create_engine(settings.database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        print("🔧 Fixing integration permissions...")
        
        # Get current admin role permissions
        result = session.execute(
            text("SELECT id, name, permissions FROM roles WHERE name = 'admin'")
        ).fetchone()
        
        if result:
            admin_id, admin_name, admin_permissions = result
            # Handle both JSON string and list formats
            if isinstance(admin_permissions, str):
                admin_permissions = json.loads(admin_permissions) if admin_permissions else []
            elif admin_permissions is None:
                admin_permissions = []

            # Add MANAGE_INTEGRATIONS if not present
            if "integrations:manage" not in admin_permissions:
                admin_permissions.append("integrations:manage")
                session.execute(
                    text("UPDATE roles SET permissions = :permissions WHERE id = :id"),
                    {"permissions": json.dumps(admin_permissions), "id": admin_id}
                )
                print(f"✓ Added integrations:manage to {admin_name} role")
            else:
                print(f"✓ {admin_name} role already has integrations:manage permission")
        
        # Get current manager role permissions
        result = session.execute(
            text("SELECT id, name, permissions FROM roles WHERE name = 'manager'")
        ).fetchone()
        
        if result:
            manager_id, manager_name, manager_permissions = result
            # Handle both JSON string and list formats
            if isinstance(manager_permissions, str):
                manager_permissions = json.loads(manager_permissions) if manager_permissions else []
            elif manager_permissions is None:
                manager_permissions = []

            # Add MANAGE_INTEGRATIONS if not present
            if "integrations:manage" not in manager_permissions:
                manager_permissions.append("integrations:manage")
                session.execute(
                    text("UPDATE roles SET permissions = :permissions WHERE id = :id"),
                    {"permissions": json.dumps(manager_permissions), "id": manager_id}
                )
                print(f"✓ Added integrations:manage to {manager_name} role")
            else:
                print(f"✓ {manager_name} role already has integrations:manage permission")
        
        # Commit changes
        session.commit()
        print("✅ Integration permissions fixed successfully!")
        
        # Show current permissions for verification
        print("\n📋 Current role permissions:")
        roles = session.execute(
            text("SELECT name, permissions FROM roles ORDER BY name")
        ).fetchall()
        
        for role_name, permissions in roles:
            # Handle both JSON string and list formats
            if isinstance(permissions, str):
                permissions = json.loads(permissions) if permissions else []
            elif permissions is None:
                permissions = []
            print(f"\n{role_name}:")
            for perm in sorted(permissions):
                print(f"  - {perm}")
        
    except Exception as e:
        session.rollback()
        print(f"❌ Error fixing permissions: {e}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    fix_integration_permissions()
