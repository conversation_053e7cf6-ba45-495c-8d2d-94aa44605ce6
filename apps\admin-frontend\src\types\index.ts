export interface User {
  id: string;
  email: string;
  is_2fa_enabled: boolean;
  tenants: TenantInfo[];
}

export interface TenantInfo {
  id: string;
  name: string;
  role: string;
  permissions: string[];
}

export interface LoginResponse {
  access_token?: string;
  temp_token?: string;
  token_type: string;
  requires_2fa: boolean;
  message: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface Invoice {
  id: string;
  supplier_name: string;
  invoice_number?: string;
  invoice_date?: string;
  due_date?: string;
  total_amount?: number;
  currency?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'needs_review';
  created_at: string;
  updated_at: string;
  original_filename: string;
  extracted_text?: string;
  extracted_context?: string;
  processing_error?: string;
  accounting_entries?: AccountingEntry[];
}

export interface AccountingEntry {
  id: string;
  account_code: string;
  account_name: string;
  debit_amount?: number;
  credit_amount?: number;
  description?: string;
  confidence_score: number;
  is_validated: boolean;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'review' | 'validation' | 'error' | 'manual_entry';
  is_completed: boolean;
  created_at: string;
  invoice_id?: string;
  assigned_user?: {
    id: string;
    email: string;
  };
  invoice?: {
    id: string;
    supplier_name: string;
    status: string;
  };
}

export interface TwoFASetupResponse {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

export interface ApiError {
  detail: string;
}

// Utility function to combine CSS class names
export function classNames(...classes: string[]): string {
  return classes.filter(Boolean).join(' ');
}
