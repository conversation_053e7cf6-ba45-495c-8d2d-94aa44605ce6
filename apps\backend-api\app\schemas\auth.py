from pydantic import BaseModel, field_validator
from typing import Optional, List
from uuid import UUID
from app.utils.email_validator import pydantic_email_validator
from app.schemas.base import BaseResponseModel, BaseCreateModel


class LoginRequest(BaseCreateModel):
    email: str
    password: str

    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email using our custom validator."""
        return pydantic_email_validator(v)


class LoginResponse(BaseResponseModel):
    access_token: Optional[str] = None
    temp_token: Optional[str] = None
    token_type: str = "bearer"
    requires_2fa: bool = False
    message: str


class TwoFAVerifyRequest(BaseCreateModel):
    code: str


class TwoFASetupResponse(BaseResponseModel):
    secret: str
    qr_code: str
    backup_codes: List[str]


class TwoFAEnableRequest(BaseCreateModel):
    code: str


class TwoFADisableRequest(BaseCreateModel):
    password: str
    code: str


class TokenResponse(BaseResponseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class UserInfo(BaseResponseModel):
    id: UUID
    email: str
    is_2fa_enabled: bool
    tenants: List['TenantInfo']

    class Config:
        from_attributes = True


class TenantInfo(BaseResponseModel):
    id: UUID
    name: str
    role: str
    permissions: List[str]

    class Config:
        from_attributes = True


class RefreshTokenRequest(BaseCreateModel):
    refresh_token: str


# Update forward references
UserInfo.model_rebuild()
