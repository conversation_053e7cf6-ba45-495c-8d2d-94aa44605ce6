from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>SO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy_utils import EncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import AesEngine
from .base import BaseModel
from app.config import settings


class User(BaseModel):
    """User model with 2FA support"""
    __tablename__ = "users"

    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # 2FA fields - encrypted at rest
    is_2fa_enabled = Column(Boolean, default=False, nullable=False)
    mfa_secret = Column(
        EncryptedType(String, settings.encryption_key, AesEngine, 'pkcs5'),
        nullable=True
    )
    mfa_recovery_codes = Column(
        EncryptedType(JSON, settings.encryption_key, AesEngine, 'pkcs5'),
        nullable=True
    )
    
    # Relationships
    tenant_users = relationship("TenantUser", back_populates="user", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}')>"


class Role(BaseModel):
    """Role model for RBAC"""
    __tablename__ = "roles"

    name = Column(String(100), unique=True, nullable=False)
    description = Column(String(255), nullable=True)
    permissions = Column(JSON, nullable=False, default=list)  # List of permission strings
    
    # Relationships
    tenant_users = relationship("TenantUser", back_populates="role")

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"


class TenantUser(BaseModel):
    """Association table for User-Tenant-Role relationship"""
    __tablename__ = "tenant_users"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="tenant_users")
    tenant = relationship("Tenant", back_populates="tenant_users")
    role = relationship("Role", back_populates="tenant_users")

    def __repr__(self):
        return f"<TenantUser(user_id={self.user_id}, tenant_id={self.tenant_id}, role_id={self.role_id})>"
