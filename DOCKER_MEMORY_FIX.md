# Docker Memory Allocation Fix för Backend-API

## Problem
Backend-API:et kraschade med följande fel när det startades i Docker:

```
_rust_notify.WatchfilesRustInternalError: error in underlying watcher: Cannot allocate memory (os error 12)
```

Detta fel uppstod på grund av att uvicorn's file watcher försökte allokera för mycket minne i Docker-miljön.

## Lösning

### 1. Uppdaterad Docker Compose Konfiguration
Lade till minnesbegränsningar och förbättrade reload-flaggor i `docker-compose.yml`:

```yaml
backend-api:
  # ... andra inställningar ...
  deploy:
    resources:
      limits:
        memory: 1G
      reservations:
        memory: 512M
  command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-exclude="*.pyc" --reload-exclude="__pycache__/*" --reload-exclude=".git/*" --reload-exclude="node_modules/*" --reload-exclude="*.log"
```

### 2. Uppdaterad Dockerfile
Förbättrade file watching i `apps/backend-api/Dockerfile`:

```dockerfile
# Development command with hot reload and memory-friendly file watching
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "/app", "--reload-exclude", "*.pyc", "--reload-exclude", "__pycache__/*", "--reload-exclude", ".git/*", "--reload-exclude", "node_modules/*", "--reload-exclude", "*.log"]
```

### 3. Alternativ Säker Konfiguration
Skapade `docker-compose.dev-safe.yml` som använder polling istället för file watching:

```yaml
# Use polling instead of file watching to avoid memory issues
command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-delay 2 --use-colors --log-level info
```

### 4. Förbättrad File Watcher
Lade till `watchdog==3.0.0` i `requirements.txt` för en mer minnesvänlig file watcher.

## Användning

### Standard Konfiguration (Rekommenderad)
```bash
docker-compose up backend-api db redis
```

### Säker Konfiguration (Om problem kvarstår)
```bash
docker-compose -f docker-compose.dev-safe.yml up backend-api db redis
```

## Fördelar med Lösningen

1. **Minnesbegränsningar**: Förhindrar att containern använder för mycket minne
2. **Exkluderade filer**: File watchern ignorerar onödiga filer som `.pyc`, `__pycache__`, etc.
3. **Backup-lösning**: Alternativ konfiguration med polling om problem kvarstår
4. **Bibehållen funktionalitet**: Hot reload fungerar fortfarande för utveckling

## Testning
API:et svarar korrekt på `http://localhost:8000/health` och visar:
```json
{"status":"healthy","environment":"development"}
```

## Tekniska Detaljer

### Vad orsakade problemet?
- uvicorn's file watcher (baserad på Rust's notify) försökte övervaka för många filer
- Docker-miljön hade begränsade minnesresurser
- File watchern allokerade mer minne än vad som var tillgängligt

### Hur löser vi det?
- Begränsar minnestilldelning för Docker-containern
- Exkluderar onödiga filer från file watching
- Använder mer effektiva reload-flaggor
- Tillhandahåller alternativ med polling istället för file watching
