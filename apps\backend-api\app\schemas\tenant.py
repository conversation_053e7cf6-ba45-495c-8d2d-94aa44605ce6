"""Tenant schemas for API requests and responses"""
from typing import Optional
from pydantic import Field
from app.schemas.base import BaseResponseModel, BaseUpdateModel
from app.models.tenant import TenantType


class TenantResponse(BaseResponseModel):
    """Response schema for tenant data"""
    name: str
    domain: Optional[str] = None
    is_active: bool
    settings: Optional[str] = None
    company_context: Optional[str] = None
    tenant_type: TenantType


class TenantUpdate(BaseUpdateModel):
    """Schema for updating tenant data"""
    company_context: Optional[str] = Field(None, description="Company information and context")
    tenant_type: Optional[TenantType] = Field(None, description="Type of tenant (standard or agency)")
