import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '../contexts/AuthContext';

// Mock the API
jest.mock('../services/api', () => ({
  authApi: {
    login: jest.fn(),
    verifyTwoFA: jest.fn(),
    getCurrentUser: jest.fn(),
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test component that uses the auth context
const TestComponent = () => {
  const { user, isAuthenticated, login, verifyTwoFA, logout } = useAuth();

  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <div data-testid="user-email">{user?.email || 'no-user'}</div>
      <button onClick={() => login('<EMAIL>', 'password')}>
        Login
      </button>
      <button onClick={() => verifyTwoFA('123456', 'temp-token')}>
        Verify 2FA
      </button>
      <button onClick={logout}>Logout</button>
    </div>
  );
};

const renderWithAuth = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('initializes with unauthenticated state', () => {
    renderWithAuth();
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
    expect(screen.getByTestId('user-email')).toHaveTextContent('no-user');
  });

  it('handles successful login without 2FA', async () => {
    const { authApi } = require('../services/api');
    authApi.login.mockResolvedValue({
      access_token: 'test-token',
      requires_2fa: false,
      message: 'Login successful'
    });
    authApi.getCurrentUser.mockResolvedValue({
      id: '1',
      email: '<EMAIL>',
      is_2fa_enabled: false,
      tenants: [{ id: 'tenant-1', name: 'Test Tenant' }]
    });

    renderWithAuth();
    
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);

    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith('access_token', 'test-token');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('current_tenant_id', 'tenant-1');
    });

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
    });
  });

  it('handles login with 2FA required', async () => {
    const { authApi } = require('../services/api');
    authApi.login.mockResolvedValue({
      temp_token: 'temp-token',
      requires_2fa: true,
      message: '2FA verification required'
    });

    renderWithAuth();
    
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);

    await waitFor(() => {
      expect(authApi.login).toHaveBeenCalledWith('<EMAIL>', 'password');
    });

    // Should not set access token yet
    expect(localStorageMock.setItem).not.toHaveBeenCalledWith('access_token', expect.anything());
  });

  it('handles successful 2FA verification', async () => {
    const { authApi } = require('../services/api');
    authApi.verifyTwoFA.mockResolvedValue({
      access_token: 'test-token',
      refresh_token: 'refresh-token'
    });
    authApi.getCurrentUser.mockResolvedValue({
      id: '1',
      email: '<EMAIL>',
      is_2fa_enabled: true,
      tenants: [{ id: 'tenant-1', name: 'Test Tenant' }]
    });

    renderWithAuth();
    
    const verifyButton = screen.getByText('Verify 2FA');
    fireEvent.click(verifyButton);

    await waitFor(() => {
      expect(authApi.verifyTwoFA).toHaveBeenCalledWith('123456', 'temp-token');
    });

    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith('access_token', 'test-token');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('refresh_token', 'refresh-token');
    });

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
    });
  });

  it('handles logout', async () => {
    // First set up authenticated state
    localStorageMock.getItem.mockReturnValue('test-token');
    const { authApi } = require('../services/api');
    authApi.getCurrentUser.mockResolvedValue({
      id: '1',
      email: '<EMAIL>',
      is_2fa_enabled: false,
      tenants: []
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
    });

    const logoutButton = screen.getByText('Logout');
    fireEvent.click(logoutButton);

    expect(localStorageMock.removeItem).toHaveBeenCalledWith('access_token');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('refresh_token');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('current_tenant_id');

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent('no-user');
    });
  });

  it('restores authentication state from localStorage', async () => {
    localStorageMock.getItem.mockReturnValue('stored-token');
    const { authApi } = require('../services/api');
    authApi.getCurrentUser.mockResolvedValue({
      id: '1',
      email: '<EMAIL>',
      is_2fa_enabled: false,
      tenants: []
    });

    renderWithAuth();

    await waitFor(() => {
      expect(authApi.getCurrentUser).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
    });
  });

  it('handles authentication errors gracefully', async () => {
    const { authApi } = require('../services/api');
    authApi.login.mockRejectedValue({
      response: { data: { detail: 'Invalid credentials' } }
    });

    renderWithAuth();
    
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);

    await waitFor(() => {
      expect(authApi.login).toHaveBeenCalled();
    });

    // Should remain unauthenticated
    expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
  });
});
