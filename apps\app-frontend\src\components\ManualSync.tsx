import React from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  ArrowPathIcon,
  CloudArrowDownIcon
} from '@heroicons/react/24/outline';
import { integrationsApi } from '../services/api';
import toast from 'react-hot-toast';

interface ManualSyncProps {
  className?: string;
}

export default function ManualSync({ className = '' }: ManualSyncProps) {
  const queryClient = useQueryClient();

  const { data: integrations } = useQuery(
    'integrations',
    integrationsApi.getIntegrations
  );

  const syncAllMutation = useMutation(integrationsApi.syncAllIntegrations, {
    onSuccess: (response) => {
      queryClient.invalidateQueries('integrations');

      if (response.success) {
        toast.success(response.message);
      } else {
        toast.error('Sync task failed to start');
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to start sync task');
    },
  });

  const handleSyncAll = () => {
    syncAllMutation.mutate();
  };

  const activeIntegrations = integrations?.filter(i => i.is_active) || [];
  const isLoading = syncAllMutation.isLoading;

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="card-container">
        <h3 className="section-title flex items-center mb-6">
          <ArrowPathIcon className="h-6 w-6 mr-2 text-indigo-600" />
          Manual Sync
        </h3>

        <div className="bg-gradient-to-r from-green-50 via-blue-50 to-indigo-50 rounded-2xl p-6">
          {activeIntegrations.length === 0 ? (
            <div className="text-center py-8">
              <CloudArrowDownIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No active integration found</p>
              <p className="text-sm text-gray-400">
                Please configure and activate an integration to enable manual sync.
              </p>
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Sync Integration</h4>
                  <p className="text-sm text-gray-600">
                    Manually trigger sync for your active integration
                  </p>
                </div>
                <button
                  onClick={handleSyncAll}
                  disabled={isLoading}
                  className="btn-primary flex items-center !w-40"
                >
                  {syncAllMutation.isLoading ? (
                    <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                  ) : (
                    <CloudArrowDownIcon className="h-5 w-5 mr-2" />
                  )}
                  {syncAllMutation.isLoading ? 'Starting...' : 'Sync Now'}
                </button>
              </div>

              {/* Info about background processing */}
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-700">
                  <strong>Note:</strong> Sync runs in the background. New invoices will be automatically processed and appear in your invoice list.
                </p>
              </div>
            </div>
          )}

        </div>
      </div>
    </div>
  );
}
