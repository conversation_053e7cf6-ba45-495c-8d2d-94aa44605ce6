import { useAuth } from '../contexts/AuthContext';

export interface Permission {
  name: string;
  description?: string;
}

export function usePermissions() {
  const { user } = useAuth();

  const hasPermission = (permission: string): boolean => {
    if (!user || !user.role || !user.role.permissions) {
      return false;
    }

    // Check if user has the specific permission
    return user.role.permissions.some((p: Permission) => p.name === permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissions: user?.role?.permissions || []
  };
}
