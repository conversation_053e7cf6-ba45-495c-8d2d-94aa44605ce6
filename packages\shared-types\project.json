{"name": "shared-types", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/shared-types/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/shared-types", "main": "packages/shared-types/src/index.ts", "tsConfig": "packages/shared-types/tsconfig.lib.json", "assets": ["packages/shared-types/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/shared-types/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/shared-types/jest.config.ts", "passWithNoTests": true}}}, "tags": ["scope:shared", "type:lib"]}