{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/eslint.config.js"]}}, "generators": {"@nx/react": {"application": {"style": "css", "linter": "eslint", "bundler": "webpack", "babel": true}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint"}}}, "projects": {"backend-api": "apps/backend-api", "admin-frontend": "apps/admin-frontend", "app-frontend": "apps/app-frontend", "ui-components": "packages/ui-components", "shared-types": "packages/shared-types"}}