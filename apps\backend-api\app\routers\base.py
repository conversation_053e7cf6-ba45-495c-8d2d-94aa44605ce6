"""
Base router functionality for consistent endpoint patterns
"""
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.orm import Session
from typing import TypeVar, Generic, Type, List, Optional
from uuid import UUID

from app.models.user import TenantUser
from app.models.base import BaseModel


ModelType = TypeVar("ModelType", bound=BaseModel)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")
ResponseSchemaType = TypeVar("ResponseSchemaType")


class BaseTenantRouter(Generic[ModelType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType]):
    """
    Base class for tenant-scoped CRUD operations
    
    Provides consistent patterns for:
    - Tenant isolation
    - Error handling
    - Common CRUD operations
    """
    
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    def get_by_id(
        self,
        db: Session,
        tenant_user: TenantUser,
        item_id: str
    ) -> ModelType:
        """Get item by ID with tenant isolation"""
        try:
            uuid_id = UUID(item_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid ID format"
            )
        
        item = db.query(self.model).filter(
            self.model.id == uuid_id,
            self.model.tenant_id == tenant_user.tenant_id
        ).first()
        
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{self.model.__name__} not found"
            )
        
        return item
    
    def list_items(
        self,
        db: Session,
        tenant_user: TenantUser,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[dict] = None
    ) -> List[ModelType]:
        """List items with tenant isolation and pagination"""
        query = db.query(self.model).filter(
            self.model.tenant_id == tenant_user.tenant_id
        )
        
        # Apply additional filters if provided
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    query = query.filter(getattr(self.model, field) == value)
        
        return query.offset(skip).limit(limit).all()
    
    def create_item(
        self,
        db: Session,
        tenant_user: TenantUser,
        create_data: CreateSchemaType
    ) -> ModelType:
        """Create new item with tenant isolation"""
        # Convert Pydantic model to dict
        item_data = create_data.model_dump() if hasattr(create_data, 'model_dump') else create_data.dict()
        
        # Add tenant_id
        item_data['tenant_id'] = tenant_user.tenant_id
        
        # Create model instance
        item = self.model(**item_data)
        
        db.add(item)
        db.commit()
        db.refresh(item)
        
        return item
    
    def update_item(
        self,
        db: Session,
        tenant_user: TenantUser,
        item_id: str,
        update_data: UpdateSchemaType
    ) -> ModelType:
        """Update item with tenant isolation"""
        item = self.get_by_id(db, tenant_user, item_id)
        
        # Convert Pydantic model to dict, excluding unset fields
        update_dict = update_data.model_dump(exclude_unset=True) if hasattr(update_data, 'model_dump') else update_data.dict(exclude_unset=True)
        
        # Update fields
        for field, value in update_dict.items():
            if hasattr(item, field):
                setattr(item, field, value)
        
        db.commit()
        db.refresh(item)
        
        return item
    
    def delete_item(
        self,
        db: Session,
        tenant_user: TenantUser,
        item_id: str
    ) -> bool:
        """Delete item with tenant isolation"""
        item = self.get_by_id(db, tenant_user, item_id)
        
        db.delete(item)
        db.commit()
        
        return True


def handle_database_error(error: Exception) -> HTTPException:
    """
    Centralized database error handling
    
    Converts common database errors to appropriate HTTP responses
    """
    error_msg = str(error)
    
    if "duplicate key" in error_msg.lower():
        return HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource already exists"
        )
    elif "foreign key" in error_msg.lower():
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid reference to related resource"
        )
    elif "not null" in error_msg.lower():
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Required field is missing"
        )
    else:
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database operation failed"
        )
