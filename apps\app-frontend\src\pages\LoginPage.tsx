import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import '../appCustomStyles.css';


interface LoginForm {
  email: string;
  password: string;
}

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>();

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true);
    try {
      const result = await login(data.email, data.password);

      if (result.requiresTwoFA) {
        sessionStorage.setItem('temp_token', result.tempToken!);
        navigate('/2fa-verify');
      } else {
        navigate('/dashboard');
      }
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 p-8 bg-white bg-opacity-90 rounded-3xl shadow-2xl border border-purple-200">
        <div>
          <div className="logo-and-text mb-6">
            <img className="small-logo-50" src="data:image/png;base64,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" alt="aggi-logo" />
            <h1 className="text-center text-4xl font-extrabold text-indigo-700 mb-3 tracking-wide leading-relaxed custom-gradient-text">aggi</h1>
          </div>
          <h2 className="text-center text-3xl font-semibold text-gray-800 mb-1">
            Logga in
          </h2>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="rounded-3xl shadow-lg py-6 px-5 space-y-6 bg-gradient-to-r from-purple-100 via-indigo-100 to-pink-100">
            <div className="relative">
              <label htmlFor="email" className="sr-only">
                Email
              </label>
              <input
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                })}
                type="email"
                autoComplete="email"
                className="input-custom"
                placeholder="Email"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>
            
            <div className="relative">
              <label htmlFor="password" className="sr-only">
                Lösenord
              </label>
              <input
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                className="input-custom pr-12"
                placeholder="Lösenord"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-indigo-600 hover:text-indigo-800 transition-colors"
                onClick={() => setShowPassword(!showPassword)}
                aria-label="Toggle password visibility"
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-6 w-6" />
                ) : (
                  <EyeIcon className="h-6 w-6" />
                )}
              </button>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-5 border border-transparent text-lg font-semibold rounded-3xl text-white bg-indigo-600 hover:from-indigo-700 hover:via-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed transition-all"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Loggar in...
                </div>
              ) : (
                'Logga in'
              )}
            </button>
          </div>

          <div className="text-center mt-4">
            <p className="text-sm text-gray-600">
              Demo credentials: <EMAIL> / password123
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}