# Aggie Local Development Setup
# Database & Redis in Docker, Backend & Frontend locally

Write-Host "Starting Aggie Local Development Environment..." -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "apps\backend-api")) {
    Write-Host "Error: Please run this script from the aggie root directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 1: Start Docker services (DB + Redis + Celery)
Write-Host ""
Write-Host "Step 1: Starting Database, Redis, and Celery services in Docker..." -ForegroundColor Cyan
docker-compose up db redis worker scheduler -d
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to start Docker services" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "Docker services (DB, <PERSON>is, Celery Worker, Celery Beat) started successfully!" -ForegroundColor Green
Start-Sleep -Seconds 3

# Step 2: Setup Backend Dependencies
Write-Host ""
Write-Host "Step 2: Setting up Backend..." -ForegroundColor Cyan
Set-Location "apps\backend-api"

# Check virtual environment
if (-not (Test-Path "venv\Scripts\Activate.ps1")) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Yellow
    python -m venv venv
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to create virtual environment" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& ".\venv\Scripts\Activate.ps1"

# Check if dependencies are installed
$needsInstall = $false
try {
    python -c "import fastapi, uvicorn, sqlalchemy, redis, qrcode" 2>$null
    if ($LASTEXITCODE -ne 0) { $needsInstall = $true }
} catch {
    $needsInstall = $true
}

if ($needsInstall) {
    Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
    python -m pip install --upgrade pip
    
    # Install packages individually to handle Windows issues
    pip install fastapi==0.104.1
    pip install "uvicorn[standard]==0.24.0"
    pip install sqlalchemy==2.0.23
    pip install redis==5.0.1
    pip install "qrcode[pil]==7.4.2"
    pip install python-dotenv==1.0.0
    pip install pydantic==2.5.0
    pip install pydantic-settings==2.1.0
    pip install "python-jose[cryptography]==3.3.0"
    pip install "passlib[bcrypt]==1.7.4"
    pip install python-multipart==0.0.6
    
    # Try PostgreSQL adapter with multiple approaches
    $psycopgInstalled = $false

    Write-Host "Installing PostgreSQL adapter..." -ForegroundColor Yellow
    try {
        pip install psycopg2-binary==2.9.7
        python -c "import psycopg2" 2>$null
        if ($LASTEXITCODE -eq 0) {
            $psycopgInstalled = $true
            Write-Host "PostgreSQL adapter installed successfully" -ForegroundColor Green
        }
    } catch { }

    if (-not $psycopgInstalled) {
        try {
            pip install --only-binary=psycopg2-binary psycopg2-binary
            python -c "import psycopg2" 2>$null
            if ($LASTEXITCODE -eq 0) {
                $psycopgInstalled = $true
                Write-Host "PostgreSQL adapter installed successfully" -ForegroundColor Green
            }
        } catch { }
    }

    if (-not $psycopgInstalled) {
        Write-Host "Warning: PostgreSQL adapter failed to install" -ForegroundColor Yellow
        Write-Host "Will use SQLite for local development instead" -ForegroundColor Yellow
    }
    
    Write-Host "Backend dependencies installed!" -ForegroundColor Green
} else {
    Write-Host "Backend dependencies already installed" -ForegroundColor Green
}

Set-Location "..\..\"

# Step 3: Setup Frontend Dependencies
Write-Host ""
Write-Host "Step 3: Setting up Frontend..." -ForegroundColor Cyan

# Check if node_modules exist
$needsFrontendInstall = $false
if (-not (Test-Path "apps\app-frontend\node_modules") -or -not (Test-Path "apps\admin-frontend\node_modules")) {
    $needsFrontendInstall = $true
}

if ($needsFrontendInstall) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    
    # Root dependencies
    if (Test-Path "package.json") {
        npm install
    }
    
    # App frontend
    Set-Location "apps\app-frontend"
    npm install
    Set-Location "..\..\"
    
    # Admin frontend
    Set-Location "apps\admin-frontend"
    npm install
    Set-Location "..\..\"
    
    Write-Host "Frontend dependencies installed!" -ForegroundColor Green
} else {
    Write-Host "Frontend dependencies already installed" -ForegroundColor Green
}

# Step 4: Start all services
Write-Host ""
Write-Host "Step 4: Starting all services..." -ForegroundColor Cyan

# Start Backend
Write-Host "Starting Backend API (Port 8000)..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\apps\backend-api'; & '.\venv\Scripts\Activate.ps1'; `$env:DATABASE_URL='postgresql://postgres:postgres@localhost:5432/aggie_dev'; `$env:REDIS_URL='redis://localhost:6379/0'; python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000" -WindowStyle Normal

Start-Sleep -Seconds 2

# Start App Frontend
Write-Host "Starting App Frontend (Port 3002)..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\apps\app-frontend'; `$env:REACT_APP_API_URL='http://localhost:8000'; `$env:PORT='3002'; npm start" -WindowStyle Normal

Start-Sleep -Seconds 2

# Start Admin Frontend
Write-Host "Starting Admin Frontend (Port 3001)..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\apps\admin-frontend'; `$env:REACT_APP_API_URL='http://localhost:8000'; `$env:PORT='3001'; npm start" -WindowStyle Normal



Write-Host ""
Write-Host "All services started!" -ForegroundColor Green
Write-Host ""
Write-Host "Service URLs:" -ForegroundColor Cyan
Write-Host "  Admin Console: http://localhost:3001" -ForegroundColor White
Write-Host "  User App: http://localhost:3002" -ForegroundColor White
Write-Host "  Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "  API Docs: http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "Docker Services:" -ForegroundColor Cyan
Write-Host "  Database: localhost:5432" -ForegroundColor White
Write-Host "  Redis: localhost:6379" -ForegroundColor White
Write-Host "  Celery Worker: Running in Docker (task processing)" -ForegroundColor White
Write-Host "  Celery Beat: Running in Docker (scheduled tasks)" -ForegroundColor White
Write-Host ""
Write-Host "To stop Docker services: docker-compose down" -ForegroundColor Yellow
Write-Host "To view Celery logs: docker-compose logs worker scheduler" -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to continue"
