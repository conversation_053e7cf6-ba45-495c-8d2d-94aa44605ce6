from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional
from uuid import UUID
import logging

from app.dependencies.auth import CurrentTenantUser, TenantScopedDB
from app.models.action_item import ActionItem
from app.models.user import TenantUser
from app.utils.permissions import Permission, check_permission
from app.routers.base import handle_database_error
from app.schemas.action_item import ActionItemResolution, ActionItemResolutionResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[dict])
async def list_action_items(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    skip: int = 0,
    limit: int = 100,
    completed: Optional[bool] = None,
    priority: Optional[str] = None,
    category: Optional[str] = None
):
    """List action items for current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    query = db.query(ActionItem).options(
        joinedload(ActionItem.user),
        joinedload(ActionItem.invoice)
    ).filter(ActionItem.tenant_id == tenant_user.tenant_id)
    
    # Apply filters
    if completed is not None:
        query = query.filter(ActionItem.is_completed == completed)
    
    if priority:
        query = query.filter(ActionItem.priority == priority)
    
    if category:
        query = query.filter(ActionItem.category == category)
    
    # Order by priority and creation date
    priority_order = {"urgent": 1, "high": 2, "medium": 3, "low": 4}
    action_items = query.offset(skip).limit(limit).all()
    
    # Sort by priority and creation date
    action_items.sort(key=lambda x: (priority_order.get(x.priority, 5), x.created_at))
    
    return [
        {
            "id": item.id,
            "title": item.title,
            "description": item.description,
            "priority": item.priority,
            "category": item.category,
            "is_completed": item.is_completed,
            "created_at": item.created_at,
            "invoice_id": item.invoice_id,
            "assigned_user": {
                "id": item.user.id,
                "email": item.user.email
            } if item.user else None,
            "invoice": {
                "id": item.invoice.id,
                "supplier_name": item.invoice.supplier_name,
                "status": item.invoice.status
            } if item.invoice else None
        }
        for item in action_items
    ]


@router.get("/{action_item_id}")
async def get_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Get action item details"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    return {
        "id": action_item.id,
        "title": action_item.title,
        "description": action_item.description,
        "priority": action_item.priority,
        "category": action_item.category,
        "is_completed": action_item.is_completed,
        "resolution_notes": action_item.resolution_notes,
        "created_at": action_item.created_at,
        "updated_at": action_item.updated_at,
        "invoice_id": action_item.invoice_id,
        "assigned_user": {
            "id": action_item.user.id,
            "email": action_item.user.email
        } if action_item.user else None,
        "completed_by": action_item.completed_by,
        "invoice": {
            "id": action_item.invoice.id,
            "supplier_name": action_item.invoice.supplier_name,
            "status": action_item.invoice.status
        } if action_item.invoice else None
    }


@router.get("/{action_item_id}/details")
async def get_action_item_details(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Get detailed action item information including invoice and session data for HITL workflow"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()

    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )

    # Prepare response data
    response_data = {
        "action_item": {
            "id": action_item.id,
            "title": action_item.title,
            "description": action_item.description,
            "priority": action_item.priority,
            "category": action_item.category,
            "is_completed": action_item.is_completed,
            "created_at": action_item.created_at,
            "updated_at": action_item.updated_at
        },
        "invoice": None,
        "session": None,
        "extracted_lines": []
    }

    # Add invoice data if available
    if action_item.invoice:
        response_data["invoice"] = {
            "id": action_item.invoice.id,
            "supplier_name": action_item.invoice.supplier_name,
            "invoice_number": action_item.invoice.invoice_number,
            "invoice_date": action_item.invoice.invoice_date,
            "due_date": action_item.invoice.due_date,
            "total_amount": action_item.invoice.total_amount,
            "currency": action_item.invoice.currency,
            "file_data": action_item.invoice.file_data,
            "file_type": action_item.invoice.file_type,
            "status": action_item.invoice.status
        }

    # Add session data if available
    if action_item.session:
        response_data["session"] = {
            "id": action_item.session.id,
            "status": action_item.session.status,
            "current_step": action_item.session.current_step,
            "extracted_data": action_item.session.extracted_data,
            "context_data": action_item.session.context_data,
            "account_reasoning": action_item.session.account_reasoning
        }

        # Parse extracted data to identify invoice lines
        if action_item.session.extracted_data:
            try:
                import json
                # Try to parse as JSON first
                try:
                    extracted_json = json.loads(action_item.session.extracted_data)
                    # extracted_data is now always JSON, so use it directly
                    if isinstance(extracted_json, dict):
                        extracted_text = json.dumps(extracted_json, ensure_ascii=False, indent=2)
                    else:
                        extracted_text = str(extracted_json)
                except json.JSONDecodeError:
                    # Fallback for old format
                    extracted_text = action_item.session.extracted_data

                # Extract lines from the text (this is a simple implementation)
                # In a real scenario, you might want more sophisticated parsing
                lines = []
                for line in extracted_text.split('\n'):
                    line = line.strip()
                    if line and ('kr' in line.lower() or '€' in line or '$' in line):
                        lines.append({
                            "description": line,
                            "amount": None,  # Could be parsed from the line
                            "account_code": None,  # To be set by user
                            "account_name": None   # To be set by user
                        })

                response_data["extracted_lines"] = lines

            except Exception as e:
                logger.warning(f"Failed to parse extracted data for action item {action_item_id}: {e}")

    return response_data


@router.put("/{action_item_id}/complete")
async def complete_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    resolution_notes: Optional[str] = None
):
    """Mark action item as completed"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    if action_item.is_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Action item is already completed"
        )
    
    # Mark as completed
    action_item.is_completed = True
    action_item.completed_by = tenant_user.user_id
    action_item.resolution_notes = resolution_notes
    
    try:
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Action item marked as completed"}


@router.put("/{action_item_id}/reopen")
async def reopen_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Reopen a completed action item"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    if not action_item.is_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Action item is not completed"
        )
    
    # Reopen
    action_item.is_completed = False
    action_item.completed_by = None
    action_item.resolution_notes = None
    
    try:
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Action item reopened"}


@router.delete("/{action_item_id}")
async def delete_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Delete an action item"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    try:
        db.delete(action_item)
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Action item deleted successfully"}


@router.post("/{action_item_id}/resolve", response_model=ActionItemResolutionResponse)
async def resolve_action_item(
    action_item_id: UUID,
    resolution: ActionItemResolution,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Resolve action item by setting accounts for invoice lines and continue processing"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()

    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )

    if action_item.is_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Action item is already completed"
        )

    if not action_item.session:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Action item is not associated with a processing session"
        )

    try:
        # Mark action item as completed
        action_item.is_completed = True
        action_item.completed_by = tenant_user.user_id
        action_item.resolution_notes = resolution.resolution_notes

        # Create account data from user input
        account_data = {
            "data": [
                {
                    "account_code": line.account_code,
                    "account_name": line.account_name,
                    "amount": line.amount,
                    "description": line.line_description,
                    "confidence": 1.0  # User-set accounts have full confidence
                }
                for line in resolution.line_accounts
            ],
            "reasoning": f"Manuellt satta konton av användare {tenant_user.user.email}"
        }

        # Update session with manual account data
        session = action_item.session
        session.account_data = account_data
        session.account_reasoning = f"Manuellt satta konton av användare {tenant_user.user.email}. {resolution.resolution_notes or ''}"

        # Create embedding for context learning
        await _create_context_embedding(db, session, resolution.line_accounts)

        # Reset session status to continue processing
        session.status = "processing"

        db.commit()

        # Continue processing from bokföra step
        from app.services.invoice_processing_service import InvoiceProcessingService
        processing_service = InvoiceProcessingService(db)

        try:
            # Continue with booking step
            await processing_service._step_bokfora(session, session.invoice)

            # Mark as completed
            session.status = "completed"
            session.current_step = None
            session.invoice.status = "completed"
            db.commit()

            processing_continued = True

        except Exception as processing_error:
            logger.error(f"Failed to continue processing for session {session.id}: {processing_error}")
            # Keep action item resolved but don't continue processing
            processing_continued = False

        return ActionItemResolutionResponse(
            message="Action item resolved successfully",
            session_id=session.id,
            processing_continued=processing_continued
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to resolve action item {action_item_id}: {e}")
        raise handle_database_error(e)


async def _create_context_embedding(db: Session, session, line_accounts):
    """Create embedding for context learning from resolved action item"""
    try:
        # Prepare context data for embedding
        supplier_name = session.invoice.supplier_name or "Okänd leverantör"

        # Determine relation (this could be enhanced with more logic)
        relation = "Leverantör"  # Default relation

        # Determine what was purchased based on account codes/names
        purchased_items = []
        for line in line_accounts:
            if line.account_name:
                purchased_items.append(line.account_name)

        what_purchased = ", ".join(purchased_items) if purchased_items else "Okänt"

        # Create context string
        context_content = f"""
        Leverantör: {supplier_name}
        Relation: {relation}
        Vad som köptes: {what_purchased}
        Kontoval: {[f"{line.account_code} - {line.account_name}" for line in line_accounts]}
        """

        # Store embedding using vector service
        from app.services.vector_service import VectorService
        from app.services.llm_provider import get_llm_service

        llm_service = get_llm_service()
        vector_service = VectorService(llm_service)

        await vector_service.store_invoice_embedding(
            db=db,
            invoice_id=str(session.invoice.id),
            tenant_id=str(session.invoice.tenant_id),
            content=context_content
        )

        logger.info(f"Created context embedding for resolved action item - session {session.id}")

    except Exception as e:
        logger.warning(f"Failed to create context embedding for session {session.id}: {e}")
        # Don't fail the main flow if embedding creation fails
