import requests
import json

# Test login and then integrations endpoint
def test_integrations_with_auth():
    base_url = "http://localhost:8000"
    
    # Try to login with test credentials
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        # Login
        login_response = requests.post(f"{base_url}/api/v1/auth/token", json=login_data)
        print(f"Login status: {login_response.status_code}")
        print(f"Login response: {login_response.text[:200]}")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            
            if login_data.get("requires_2fa"):
                print("2FA required - cannot continue test")
                return
            
            access_token = login_data.get("access_token")
            if not access_token:
                print("No access token received")
                return
            
            # Test integrations endpoint with auth
            headers = {
                "Authorization": f"Bearer {access_token}",
                "X-Tenant-ID": "some-tenant-id"  # This might need to be a real tenant ID
            }
            
            print("\n" + "="*50)
            print("Testing integrations endpoint with auth...")
            
            # Test without trailing slash
            response = requests.get(f"{base_url}/api/v1/integrations", headers=headers)
            print(f"Status (no slash): {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
            # Test with trailing slash
            response = requests.get(f"{base_url}/api/v1/integrations/", headers=headers)
            print(f"Status (with slash): {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
            # Test available integrations (no auth required)
            response = requests.get(f"{base_url}/api/v1/integrations/available")
            print(f"Available integrations status: {response.status_code}")
            print(f"Available integrations: {response.text[:200]}")
            
        else:
            print("Login failed")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_integrations_with_auth()
