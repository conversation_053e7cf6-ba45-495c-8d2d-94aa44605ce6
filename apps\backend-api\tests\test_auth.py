import pytest
from fastapi.testclient import TestClient
from app.models.user import User, Role, TenantUser
from app.models.tenant import Tenant
from app.utils.security import get_password_hash, verify_password, generate_totp_secret, verify_totp_code
import pyotp


class TestAuthentication:
    """Test authentication endpoints"""
    
    def test_login_success(self, client: TestClient, db_session, sample_user_data, sample_tenant_data):
        """Test successful login"""
        # Create tenant
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        
        # Create role
        role = Role(name="admin", description="Admin", permissions=["invoices:read"])
        db_session.add(role)
        db_session.commit()
        
        # Create user
        user_data = sample_user_data.copy()
        password = user_data.pop("password")
        user_data["hashed_password"] = get_password_hash(password)
        user = User(**user_data)
        db_session.add(user)
        db_session.commit()
        
        # Create tenant user relationship
        tenant_user = TenantUser(
            user_id=user.id,
            tenant_id=tenant.id,
            role_id=role.id,
            is_active=True
        )
        db_session.add(tenant_user)
        db_session.commit()
        
        # Test login
        response = client.post("/auth/token", json={
            "email": sample_user_data["email"],
            "password": password
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["requires_2fa"] is False
        assert data["message"] == "Login successful"
    
    def test_login_invalid_credentials(self, client: TestClient):
        """Test login with invalid credentials"""
        response = client.post("/auth/token", json={
            "email": "<EMAIL>",
            "password": "wrongpassword"
        })
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_login_with_2fa_enabled(self, client: TestClient, db_session, sample_user_data, sample_tenant_data):
        """Test login with 2FA enabled"""
        # Create tenant and role
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        
        role = Role(name="admin", description="Admin", permissions=["invoices:read"])
        db_session.add(role)
        db_session.commit()
        
        # Create user with 2FA enabled
        user_data = sample_user_data.copy()
        password = user_data.pop("password")
        user_data["hashed_password"] = get_password_hash(password)
        user_data["is_2fa_enabled"] = True
        user_data["mfa_secret"] = generate_totp_secret()
        user = User(**user_data)
        db_session.add(user)
        db_session.commit()
        
        # Create tenant user relationship
        tenant_user = TenantUser(
            user_id=user.id,
            tenant_id=tenant.id,
            role_id=role.id,
            is_active=True
        )
        db_session.add(tenant_user)
        db_session.commit()
        
        # Test login
        response = client.post("/auth/token", json={
            "email": sample_user_data["email"],
            "password": password
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "temp_token" in data
        assert data["requires_2fa"] is True
        assert "2FA verification required" in data["message"]
    
    def test_2fa_verification_success(self, client: TestClient, db_session, sample_user_data, sample_tenant_data):
        """Test successful 2FA verification"""
        # Create tenant and role
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        
        role = Role(name="admin", description="Admin", permissions=["invoices:read"])
        db_session.add(role)
        db_session.commit()
        
        # Create user with 2FA
        secret = generate_totp_secret()
        user_data = sample_user_data.copy()
        password = user_data.pop("password")
        user_data["hashed_password"] = get_password_hash(password)
        user_data["is_2fa_enabled"] = True
        user_data["mfa_secret"] = secret
        user = User(**user_data)
        db_session.add(user)
        db_session.commit()
        
        # Create tenant user relationship
        tenant_user = TenantUser(
            user_id=user.id,
            tenant_id=tenant.id,
            role_id=role.id,
            is_active=True
        )
        db_session.add(tenant_user)
        db_session.commit()
        
        # Login to get temp token
        login_response = client.post("/auth/token", json={
            "email": sample_user_data["email"],
            "password": password
        })
        temp_token = login_response.json()["temp_token"]
        
        # Generate TOTP code
        totp = pyotp.TOTP(secret)
        code = totp.now()
        
        # Verify 2FA
        response = client.post(
            "/auth/2fa/verify",
            json={"code": code},
            headers={"Authorization": f"Bearer {temp_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
    
    def test_2fa_verification_invalid_code(self, client: TestClient, db_session, sample_user_data, sample_tenant_data):
        """Test 2FA verification with invalid code"""
        # Create tenant and role
        tenant = Tenant(**sample_tenant_data)
        db_session.add(tenant)
        db_session.commit()
        
        role = Role(name="admin", description="Admin", permissions=["invoices:read"])
        db_session.add(role)
        db_session.commit()
        
        # Create user with 2FA
        secret = generate_totp_secret()
        user_data = sample_user_data.copy()
        password = user_data.pop("password")
        user_data["hashed_password"] = get_password_hash(password)
        user_data["is_2fa_enabled"] = True
        user_data["mfa_secret"] = secret
        user = User(**user_data)
        db_session.add(user)
        db_session.commit()
        
        # Create tenant user relationship
        tenant_user = TenantUser(
            user_id=user.id,
            tenant_id=tenant.id,
            role_id=role.id,
            is_active=True
        )
        db_session.add(tenant_user)
        db_session.commit()
        
        # Login to get temp token
        login_response = client.post("/auth/token", json={
            "email": sample_user_data["email"],
            "password": password
        })
        temp_token = login_response.json()["temp_token"]
        
        # Try with invalid code
        response = client.post(
            "/auth/2fa/verify",
            json={"code": "123456"},
            headers={"Authorization": f"Bearer {temp_token}"}
        )
        
        assert response.status_code == 401
        assert "Invalid 2FA code" in response.json()["detail"]
    
    def test_get_current_user(self, client: TestClient, authenticated_headers):
        """Test getting current user info"""
        headers, user, tenant, role = authenticated_headers
        
        response = client.get("/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == user.email
        assert data["is_2fa_enabled"] == user.is_2fa_enabled
        assert len(data["tenants"]) == 1
        assert data["tenants"][0]["name"] == tenant.name
    
    def test_setup_2fa(self, client: TestClient, authenticated_headers):
        """Test 2FA setup"""
        headers, user, tenant, role = authenticated_headers
        
        response = client.post("/auth/2fa/setup", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "secret" in data
        assert "qr_code" in data
        assert "backup_codes" in data
        assert len(data["backup_codes"]) == 10
    
    def test_enable_2fa(self, client: TestClient, authenticated_headers, db_session):
        """Test enabling 2FA"""
        headers, user, tenant, role = authenticated_headers
        
        # Setup 2FA first
        setup_response = client.post("/auth/2fa/setup", headers=headers)
        setup_data = setup_response.json()
        secret = setup_data["secret"]
        
        # Generate TOTP code
        totp = pyotp.TOTP(secret)
        code = totp.now()
        
        # Enable 2FA
        response = client.post(
            "/auth/2fa/enable",
            json={"code": code},
            headers=headers
        )
        
        assert response.status_code == 200
        assert "2FA enabled successfully" in response.json()["message"]
        
        # Verify user is updated
        db_session.refresh(user)
        assert user.is_2fa_enabled is True


class TestSecurity:
    """Test security utilities"""
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert verify_password(password, hashed) is True
        assert verify_password("wrongpassword", hashed) is False
    
    def test_totp_generation_and_verification(self):
        """Test TOTP secret generation and code verification"""
        secret = generate_totp_secret()
        assert len(secret) == 32
        
        # Generate code
        totp = pyotp.TOTP(secret)
        code = totp.now()
        
        # Verify code
        assert verify_totp_code(secret, code) is True
        assert verify_totp_code(secret, "123456") is False
