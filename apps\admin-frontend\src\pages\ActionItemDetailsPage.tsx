import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { ArrowLeftIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { actionItemsApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';
import toast from 'react-hot-toast';

interface InvoiceLineAccount {
  line_description: string;
  account_code: string;
  account_name: string;
  amount?: number;
}

interface InvoicePreviewProps {
  fileData: string;
  fileType: string;
}

interface InvoiceRow {
  description: string;
  quantity?: string | number;
  unit_price?: string | number;
  total_price?: string | number;
}

interface InvoiceRowsProps {
  rawData: any;
}

function InvoiceRows({ rawData }: InvoiceRowsProps) {
  if (!rawData || !rawData.invoice_rows || !Array.isArray(rawData.invoice_rows)) {
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">Invoice Rows</h3>
        <p className="text-sm text-gray-500">No invoice rows available</p>
      </div>
    );
  }

  const invoiceRows: InvoiceRow[] = rawData.invoice_rows;

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h3 className="font-medium text-gray-900 mb-4">Invoice Rows</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Unit Price
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Price
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invoiceRows.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="px-3 py-2 text-sm text-gray-900">
                  {row.description || 'N/A'}
                </td>
                <td className="px-3 py-2 text-sm text-gray-900">
                  {row.quantity || 'N/A'}
                </td>
                <td className="px-3 py-2 text-sm text-gray-900">
                  {row.unit_price || 'N/A'}
                </td>
                <td className="px-3 py-2 text-sm text-gray-900">
                  {row.total_price || 'N/A'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function InvoicePreview({ fileData, fileType }: InvoicePreviewProps) {
  if (!fileData) {
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">File Preview</h3>
        <p className="text-sm text-gray-500">No file data available</p>
      </div>
    );
  }

  const renderPreview = () => {
    
    if (fileType === 'pdf') {
      // For PDF files, create a data URL and embed
      const pdfDataUrl = `data:application/pdf;base64,${fileData}`;
      return (
        <div className="w-full h-96 border rounded">
          <embed
            src={pdfDataUrl}
            type="application/pdf"
            width="100%"
            height="100%"
            className="rounded"
          />
        </div>
      );
    } else if (['png', 'jpg', 'jpeg'].includes(fileType?.toLowerCase())) {
      // For image files, create a data URL and display as img
      const imageDataUrl = `data:image/${fileType};base64,${fileData}`;
      return (
        <div className="w-full flex justify-center">
          <img
            src={imageDataUrl}
            alt="Invoice preview"
            className="max-w-full max-h-96 object-contain rounded border"
          />
        </div>
      );
    } else {
      return (
        <p className="text-sm text-gray-500">
          Preview not available for file type: {fileType}
        </p>
      );
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h3 className="font-medium text-gray-900 mb-4">File Preview</h3>
      <p className="text-sm text-gray-500 mb-4">
        File type: {fileType || 'Unknown'}
      </p>
      {renderPreview()}
    </div>
  );
}

export default function ActionItemDetailsPage() {
  const { actionItemId } = useParams<{ actionItemId: string }>();
  const navigate = useNavigate();
  const { currentTenant } = useTenant();
  const queryClient = useQueryClient();
  
  const [lineAccounts, setLineAccounts] = useState<InvoiceLineAccount[]>([]);
  const [resolutionNotes, setResolutionNotes] = useState('');

  const { data: actionItemDetails, isLoading, error } = useQuery(
    ['actionItemDetails', actionItemId],
    () => actionItemsApi.getActionItemDetails(actionItemId!),
    {
      enabled: !!actionItemId && !!currentTenant,
      onSuccess: (data) => {
        // Initialize line accounts from extracted lines
        if (data.extracted_lines) {
          setLineAccounts(data.extracted_lines.map((line: any) => ({
            line_description: line.description,
            account_code: line.account_code || '',
            account_name: line.account_name || '',
            amount: line.amount
          })));
        }
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to load action item details');
      }
    }
  );

  const resolveMutation = useMutation(
    (data: { line_accounts: InvoiceLineAccount[]; resolution_notes?: string }) =>
      actionItemsApi.resolveActionItem(actionItemId!, data),
    {
      onSuccess: (response) => {
        queryClient.invalidateQueries(['actionItems']);
        queryClient.invalidateQueries(['actionItemDetails']);
        toast.success('Action item resolved successfully!');
        if (response.processing_continued) {
          toast.success('Invoice processing continued automatically');
        }
        navigate('/action-items');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to resolve action item');
      },
    }
  );

  const handleAccountChange = (index: number, field: keyof InvoiceLineAccount, value: string | number) => {
    setLineAccounts(prev => prev.map((line, i) => 
      i === index ? { ...line, [field]: value } : line
    ));
  };

  const handleSubmit = () => {
    // Validate that all lines have account codes
    const invalidLines = lineAccounts.filter(line => !line.account_code.trim());
    if (invalidLines.length > 0) {
      toast.error('Please set account codes for all invoice lines');
      return;
    }

    resolveMutation.mutate({
      line_accounts: lineAccounts,
      resolution_notes: resolutionNotes.trim() || undefined
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  if (error || !actionItemDetails) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-red-600">Failed to load action item details</p>
            <button
              onClick={() => navigate('/action-items')}
              className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              Back to Action Items
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/action-items')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Action Items
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              {actionItemDetails.action_item.title}
            </h1>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left side - Invoice Lines */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Invoice Lines & Account Assignment</h2>
            
            {lineAccounts.length > 0 ? (
              <div className="space-y-4">
                {lineAccounts.map((line, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <input
                          type="text"
                          value={line.line_description}
                          onChange={(e) => handleAccountChange(index, 'line_description', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Amount
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={line.amount || ''}
                          onChange={(e) => handleAccountChange(index, 'amount', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Account Code *
                        </label>
                        <input
                          type="text"
                          value={line.account_code}
                          onChange={(e) => handleAccountChange(index, 'account_code', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Account Name
                        </label>
                        <input
                          type="text"
                          value={line.account_name}
                          onChange={(e) => handleAccountChange(index, 'account_name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Resolution Notes (Optional)
                  </label>
                  <textarea
                    value={resolutionNotes}
                    onChange={(e) => setResolutionNotes(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Add any notes about the resolution..."
                  />
                </div>

                <div className="flex justify-end">
                  <button
                    onClick={handleSubmit}
                    disabled={resolveMutation.isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    {resolveMutation.isLoading ? 'Resolving...' : 'Resolve Action Item'}
                  </button>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">No invoice lines found for this action item.</p>
            )}
          </div>

          {/* Right side - Invoice Preview */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Invoice Preview</h2>
            
            {actionItemDetails.invoice ? (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-2">Invoice Information</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Supplier:</span>
                      <p className="font-medium">{actionItemDetails.invoice.supplier_name || 'Unknown'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Invoice Number:</span>
                      <p className="font-medium">{actionItemDetails.invoice.invoice_number || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Date:</span>
                      <p className="font-medium">{actionItemDetails.invoice.invoice_date || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Total Amount:</span>
                      <p className="font-medium">
                        {actionItemDetails.invoice.total_amount ? 
                          `${actionItemDetails.invoice.total_amount} ${actionItemDetails.invoice.currency || 'kr'}` : 
                          'N/A'
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Invoice rows from extracted data */}
                {actionItemDetails.invoice.raw_data && (
                  <InvoiceRows rawData={actionItemDetails.invoice.raw_data} />
                )}

                {/* Invoice file preview */}
                <InvoicePreview
                  fileData={actionItemDetails.invoice.file_data}
                  fileType={actionItemDetails.invoice.file_type}
                />
              </div>
            ) : (
              <p className="text-gray-500">No invoice information available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
