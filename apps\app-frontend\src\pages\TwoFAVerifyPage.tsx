import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { useAuth } from '../contexts/AuthContext';
import '../appCustomStyles.css';

interface TwoFAForm {
  code: string;
}

export default function TwoFAVerifyPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [tempToken, setTempToken] = useState<string | null>(null);
  const { verifyTwoFA } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<TwoFAForm>();

  const code = watch('code');

  useEffect(() => {
    const token = sessionStorage.getItem('temp_token');
    if (!token) {
      navigate('/login');
      return;
    }
    setTempToken(token);
  }, [navigate]);

  const onSubmit = async (data: TwoFAForm) => {
    if (!tempToken) return;

    setIsLoading(true);
    try {
      await verifyTwoFA(data.code, tempToken);
      sessionStorage.removeItem('temp_token');
      navigate('/dashboard');
    } catch (error) {
      // Error is handled in the auth context
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    sessionStorage.removeItem('temp_token');
    navigate('/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 p-8 bg-white bg-opacity-90 rounded-3xl shadow-2xl border border-purple-200">
        <div>
          <h1 className="text-center text-4xl font-extrabold text-indigo-700 mb-3 tracking-wide leading-relaxed custom-gradient-text">aggi</h1>
          <h2 className="text-center text-3xl font-semibold text-gray-800 mb-1">
            Two-Factor Authentication
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enter the 6-digit code from your authenticator app
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="rounded-3xl shadow-lg py-6 px-5 space-y-6 bg-gradient-to-r from-purple-100 via-indigo-100 to-pink-100">
            <div>
              <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
                Authentication Code
              </label>
              <input
                {...register('code', {
                  required: 'Authentication code is required',
                  pattern: {
                    value: /^[0-9]{6}$/,
                    message: 'Code must be 6 digits',
                  },
                })}
                type="text"
                maxLength={6}
                className="input-custom text-center text-2xl tracking-widest"
                placeholder="000000"
                autoComplete="one-time-code"
              />
              {errors.code && (
                <p className="mt-1 text-sm text-red-600">{errors.code.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <button
              type="submit"
              disabled={isLoading || !code || code.length !== 6}
              className="group relative w-full flex justify-center py-3 px-5 border border-transparent text-lg font-semibold rounded-3xl text-white bg-indigo-600 hover:from-indigo-700 hover:via-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed transition-all"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Verifying...
                </div>
              ) : (
                'Verify Code'
              )}
            </button>

            <button
              type="button"
              onClick={handleBackToLogin}
              className="btn-secondary"
            >
              Back to Login
            </button>
          </div>

          <div className="text-center mt-4">
            <p className="text-sm text-gray-600">
              Can't access your authenticator app?
            </p>
            <p className="text-xs text-gray-500 mt-1">
              You can also use a recovery code instead of the 6-digit code
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
