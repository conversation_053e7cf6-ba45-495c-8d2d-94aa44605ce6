import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TenantInfo } from '../types';
import { useAuth } from './AuthContext';

interface TenantContextType {
  currentTenant: TenantInfo | null;
  availableTenants: TenantInfo[];
  switchTenant: (tenantId: string) => void;
  hasPermission: (permission: string) => boolean;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const { user } = useAuth();
  const [currentTenant, setCurrentTenant] = useState<TenantInfo | null>(null);

  const availableTenants = user?.tenants || [];

  useEffect(() => {
    if (user && user.tenants.length > 0) {
      const savedTenantId = localStorage.getItem('current_tenant_id');

      console.log('TenantContext: Setting up tenant', {
        user: user.email,
        tenants: user.tenants.map(t => ({ id: t.id, name: t.name, role: t.role, permissions: t.permissions })),
        savedTenantId
      });

      if (savedTenantId) {
        const savedTenant = user.tenants.find(t => t.id === savedTenantId);
        if (savedTenant) {
          console.log('TenantContext: Using saved tenant', savedTenant);
          setCurrentTenant(savedTenant);
          return;
        }
      }

      // Default to first tenant
      console.log('TenantContext: Using default tenant', user.tenants[0]);
      setCurrentTenant(user.tenants[0]);
      localStorage.setItem('current_tenant_id', user.tenants[0].id);
    } else if (user) {
      console.log('TenantContext: User has no tenants', user);
    }
  }, [user]);

  const switchTenant = (tenantId: string) => {
    const tenant = availableTenants.find(t => t.id === tenantId);
    if (tenant) {
      setCurrentTenant(tenant);
      localStorage.setItem('current_tenant_id', tenantId);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!currentTenant) {
      console.log('hasPermission: No current tenant');
      return false;
    }
    const hasAccess = currentTenant.permissions.includes(permission);
    console.log('hasPermission:', {
      permission,
      currentTenant: currentTenant.name,
      userPermissions: currentTenant.permissions,
      hasAccess
    });
    return hasAccess;
  };

  const value = {
    currentTenant,
    availableTenants,
    switchTenant,
    hasPermission,
  };

  return <TenantContext.Provider value={value}>{children}</TenantContext.Provider>;
}
