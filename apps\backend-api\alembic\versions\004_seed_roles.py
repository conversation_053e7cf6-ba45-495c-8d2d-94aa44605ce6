"""Seed default roles

Revision ID: 004
Revises: 003
Create Date: 2024-01-01 10:03:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column
from sqlalchemy.dialects.postgresql import UUID
import uuid

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Define the roles table structure for data operations
    roles_table = table('roles',
        column('id', UUID),
        column('name', sa.String),
        column('description', sa.String),
        column('permissions', sa.JSON),
        column('created_at', sa.DateTime),
        column('updated_at', sa.DateTime)
    )
    
    # Insert default roles
    op.bulk_insert(roles_table, [
        {
            'id': uuid.uuid4(),
            'name': 'admin',
            'description': 'Full administrative access',
            'permissions': [
                'invoices:read', 'invoices:write', 'invoices:delete',
                'users:read', 'users:write', 'users:delete',
                'settings:read', 'settings:write',
                'reports:read', 'reports:write'
            ]
        },
        {
            'id': uuid.uuid4(),
            'name': 'manager',
            'description': 'Management access with user oversight',
            'permissions': [
                'invoices:read', 'invoices:write',
                'users:read', 'users:write',
                'reports:read', 'reports:write'
            ]
        },
        {
            'id': uuid.uuid4(),
            'name': 'accountant',
            'description': 'Accounting and invoice processing',
            'permissions': [
                'invoices:read', 'invoices:write',
                'accounting:read', 'accounting:write',
                'reports:read'
            ]
        },
        {
            'id': uuid.uuid4(),
            'name': 'viewer',
            'description': 'Read-only access',
            'permissions': [
                'invoices:read',
                'reports:read'
            ]
        }
    ])


def downgrade() -> None:
    # Remove default roles
    op.execute("DELETE FROM roles WHERE name IN ('admin', 'manager', 'accountant', 'viewer')")
