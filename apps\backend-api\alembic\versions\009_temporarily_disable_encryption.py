"""Temporarily disable encryption due to compatibility issues

Revision ID: 009
Revises: 008
Create Date: 2024-01-20 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '009'
down_revision = '008'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Temporarily disable encryption by changing column types
    # This is a temporary measure until SQLAlchemy-Utils compatibility is resolved
    
    # Note: In a production environment, you would need to decrypt existing data first
    # For now, we're assuming this is a fresh installation
    
    # Add comments to indicate temporary state
    op.execute("""
        COMMENT ON COLUMN invoice_integrations.configuration IS 
        'Configuration data - encryption temporarily disabled due to compatibility issues'
    """)
    
    op.execute("""
        COMMENT ON COLUMN oauth2_tokens.access_token IS 
        'Access token - encryption temporarily disabled due to compatibility issues'
    """)
    
    op.execute("""
        COMMENT ON COLUMN oauth2_tokens.refresh_token IS 
        'Refresh token - encryption temporarily disabled due to compatibility issues'
    """)
    
    op.execute("""
        COMMENT ON COLUMN oauth2_tokens.extra_data IS 
        'Extra data - encryption temporarily disabled due to compatibility issues'
    """)


def downgrade() -> None:
    # Remove comments
    op.execute("COMMENT ON COLUMN invoice_integrations.configuration IS NULL")
    op.execute("COMMENT ON COLUMN oauth2_tokens.access_token IS NULL")
    op.execute("COMMENT ON COLUMN oauth2_tokens.refresh_token IS NULL")
    op.execute("COMMENT ON COLUMN oauth2_tokens.extra_data IS NULL")
