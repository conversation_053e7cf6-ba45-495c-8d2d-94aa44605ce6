#!/bin/bash
# Aggie Development Server Startup Script (Mac/Linux)

echo "🚀 Starting Aggie Development Environment..."
echo "=================================================="

# Check if we're in the right directory
if [ ! -d "apps/backend-api" ] || [ ! -d "apps/app-frontend" ]; then
    echo "❌ Error: Please run this script from the aggie root directory"
    echo "Current directory: $(pwd)"
    exit 1
fi

# Function to start a service in a new terminal
start_service() {
    local title="$1"
    local command="$2"
    echo "🔄 Starting $title..."
    
    # Try different terminal emulators
    if command -v gnome-terminal >/dev/null 2>&1; then
        gnome-terminal --title="$title" -- bash -c "$command; exec bash"
    elif command -v xterm >/dev/null 2>&1; then
        xterm -title "$title" -e bash -c "$command; exec bash" &
    elif command -v osascript >/dev/null 2>&1; then
        # macOS Terminal
        osascript -e "tell application \"Terminal\" to do script \"cd $(pwd) && $command\""
    else
        echo "⚠️  Warning: No suitable terminal found. Running $title in background..."
        eval "$command" &
    fi
    
    sleep 2
}

# Start database and Redis
echo "🐳 Starting Database and Redis..."
if command -v docker-compose >/dev/null 2>&1; then
    docker-compose up db redis -d
    echo "✅ Database and Redis started"
else
    echo "⚠️  Warning: docker-compose not found. Please start PostgreSQL and Redis manually."
fi

sleep 3

# Start Backend API
start_service "Backend API (Port 8000)" "cd apps/backend-api && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

# Start App Frontend
start_service "App Frontend (Port 3000)" "cd apps/app-frontend && npm start"

# Start Admin Frontend
start_service "Admin Frontend (Port 3001)" "cd apps/admin-frontend && npm start"

# Start Celery Worker
start_service "Celery Worker" "cd apps/backend-api && celery -A app.celery_app worker --loglevel=info"

# Start Celery Beat
start_service "Celery Beat Scheduler" "cd apps/backend-api && celery -A app.celery_app beat --loglevel=info"

echo ""
echo "🎉 All development servers are starting!"
echo ""
echo "📋 Services Overview:"
echo "  🌐 App Frontend:      http://localhost:3000"
echo "  🔧 Admin Frontend:    http://localhost:3001"
echo "  🚀 Backend API:       http://localhost:8000"
echo "  📚 API Docs:          http://localhost:8000/docs"
echo "  🐳 Database:          localhost:5432"
echo "  🔴 Redis:             localhost:6379"
echo ""
echo "💡 Tips:"
echo "  • Each service runs in its own terminal window"
echo "  • Close terminal windows to stop individual services"
echo "  • Check each terminal for startup logs and errors"
echo "  • Use Ctrl+C in each terminal to stop services gracefully"
echo ""
echo "🔍 To test the new Integration Settings:"
echo "  1. Go to http://localhost:3000"
echo "  2. Login to your account"
echo "  3. Navigate to Settings > Integrations"
echo "  4. Test creating integrations and manual sync"
echo ""
echo "Press Enter to exit this script (services will continue running)..."
read
