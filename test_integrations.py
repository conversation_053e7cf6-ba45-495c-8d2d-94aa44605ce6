import requests

# Test the integrations endpoint
try:
    r = requests.get('http://localhost:8000/api/v1/integrations', allow_redirects=False)
    print(f'Status: {r.status_code}')
    print(f'Headers: {dict(r.headers)}')
    if 'location' in r.headers:
        print(f'Location: {r.headers["location"]}')
    print(f'Content: {r.text[:200]}')
except Exception as e:
    print(f'Error: {e}')

print("\n" + "="*50 + "\n")

# Test with trailing slash
try:
    r = requests.get('http://localhost:8000/api/v1/integrations/', allow_redirects=False)
    print(f'Status with slash: {r.status_code}')
    print(f'Headers: {dict(r.headers)}')
    print(f'Content: {r.text[:200]}')
except Exception as e:
    print(f'Error with slash: {e}')
