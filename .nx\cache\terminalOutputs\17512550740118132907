


[1m[33mwarn[39m[22m - The `content` option in your Tailwind CSS configuration is missing or empty.
[1m[33mwarn[39m[22m - Configure your content sources or your generated CSS will be missing styles.
[1m[33mwarn[39m[22m - https://tailwindcss.com/docs/content-configuration
Source path: D:\Visual Code Repo\aggie\apps\admin-frontend\src\index.css
Setting up new context...
Finding changed files: 0.249ms
Reading changed files: 0.004ms
Sorting candidates: 0.004ms
Generate rules: 1.74ms
Build stylesheet: 0.528ms
Potential classes:  [33m1[39m
Active contexts:  [33m1[39m
JIT TOTAL: 82.981ms


Entrypoint [1mmain[39m[22m = [1m[32mruntime.3c4b2e2608202e9c.js[39m[22m [1m[32mmain.0a6d8f6046109aab.js[39m[22m
Entrypoint [1mpolyfills[39m[22m = [1m[32mruntime.3c4b2e2608202e9c.js[39m[22m
Entrypoint [1mstyles[39m[22m = [1m[32mruntime.3c4b2e2608202e9c.js[39m[22m [1m[32mstyles.9a7e9e1fdb10db38.css[39m[22m [1m[32mstyles.176f2447e33fa700.js[39m[22m
chunk (runtime: runtime) [1m[32mruntime.3c4b2e2608202e9c.js[39m[22m (runtime) 2.48 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mmain.0a6d8f6046109aab.js[39m[22m (main) 39 bytes [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m
chunk (runtime: runtime) [1m[32mstyles.9a7e9e1fdb10db38.css[39m[22m, [1m[32mstyles.176f2447e33fa700.js[39m[22m (styles) 50 bytes (javascript) 10.3 KiB (css/mini-extract) [1m[33m[initial][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./src/index.tsx[39m[22m
[1mModule build [1m[31mfailed[39m[22m[1m (from ../../node_modules/@nx/webpack/src/utils/web-babel-loader.js):
[1m[31mError[39m[22m[1m: Cannot find module 'D:\Visual Code Repo\aggie\apps\admin-frontend\.babelrc'
Require stack:
- D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\files\configuration.js
- D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\files\index.js
- D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\index.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\node_modules\babel-loader\lib\index.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\src\utils\web-babel-loader.js
- D:\Visual Code Repo\aggie\node_modules\loader-runner\lib\loadLoader.js
- D:\Visual Code Repo\aggie\node_modules\loader-runner\lib\LoaderRunner.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\node_modules\webpack\lib\NormalModuleFactory.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\node_modules\webpack\lib\Compiler.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\node_modules\webpack\lib\webpack.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\node_modules\webpack\lib\index.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\src\executors\webpack\lib\run-webpack.js
- D:\Visual Code Repo\aggie\node_modules\@nx\webpack\src\executors\webpack\webpack.impl.js
- D:\Visual Code Repo\aggie\node_modules\nx\src\config\schema-utils.js
- D:\Visual Code Repo\aggie\node_modules\nx\src\command-line\run\executor-utils.js
- D:\Visual Code Repo\aggie\node_modules\nx\src\project-graph\utils\project-configuration-utils.js
- D:\Visual Code Repo\aggie\node_modules\nx\src\utils\package-json.js
- D:\Visual Code Repo\aggie\node_modules\nx\src\utils\print-help.js
- D:\Visual Code Repo\aggie\node_modules\nx\src\command-line\run\run.js
- D:\Visual Code Repo\aggie\node_modules\nx\bin\run-executor.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at resolve (node:internal/modules/helpers:145:19)
    at loadConfig (D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\files\configuration.js:213:5)
    at loadConfig.next (<anonymous>)
    at buildRootChain (D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\config-chain.js:64:47)
    at buildRootChain.next (<anonymous>)
    at loadPrivatePartialConfig (D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\partial.js:72:62)
    at loadPrivatePartialConfig.next (<anonymous>)
    at loadPartialConfig (D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\partial.js:115:25)
    at loadPartialConfig.next (<anonymous>)
    at step (D:\Visual Code Repo\aggie\node_modules\gensync\index.js:261:32)
    at evaluateAsync (D:\Visual Code Repo\aggie\node_modules\gensync\index.js:291:5)
    at D:\Visual Code Repo\aggie\node_modules\gensync\index.js:93:9
    at new Promise (<anonymous>)
    at async (D:\Visual Code Repo\aggie\node_modules\gensync\index.js:92:14)
    at stopHiding - secret - don't use this - v1 (D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\errors\rewrite-stack-trace.js:47:12)
    at Object.loadPartialConfigAsync (D:\Visual Code Repo\aggie\node_modules\@babel\core\lib\config\index.js:34:85)
    at Object.loader (D:\Visual Code Repo\aggie\node_modules\@nx\webpack\node_modules\babel-loader\lib\index.js:116:30)[39m[22m

[1m[31mERROR[39m[22m in [1m[32mpolyfills[39m[22m
[1mModule [1m[31mnot found[39m[22m[1m: [1m[31mError[39m[22m[1m: Can't resolve 'D:\Visual Code Repo\aggie\apps\admin-frontend\src\polyfills.ts' in 'D:\Visual Code Repo\aggie\apps\admin-frontend'[39m[22m

[1m[31mERROR[39m[22m in [1munable to locate 'D:/Visual Code Repo/aggie/apps/admin-frontend/src/assets/**/*' glob[39m[22m

webpack compiled with [1m[31m3 errors[39m[22m (70fdcca21320fd25)
