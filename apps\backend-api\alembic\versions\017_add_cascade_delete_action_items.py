"""Add cascade delete for action_items.session_id

Revision ID: 017
Revises: 016
Create Date: 2025-08-07 09:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '017'
down_revision = '016'
branch_labels = None
depends_on = None


def upgrade():
    # Drop existing foreign key constraint
    try:
        op.drop_constraint('action_items_session_id_fkey', 'action_items', type_='foreignkey')
    except:
        # Constraint might not exist or have different name, continue
        pass
    
    # Add new foreign key constraint with CASCADE delete
    op.create_foreign_key(
        'action_items_session_id_fkey',
        'action_items', 
        'sessions',
        ['session_id'], 
        ['id'],
        ondelete='CASCADE'
    )


def downgrade():
    # Drop CASCADE foreign key constraint
    op.drop_constraint('action_items_session_id_fkey', 'action_items', type_='foreignkey')
    
    # Add back original foreign key constraint without CASCADE
    op.create_foreign_key(
        'action_items_session_id_fkey',
        'action_items', 
        'sessions',
        ['session_id'], 
        ['id']
    )
