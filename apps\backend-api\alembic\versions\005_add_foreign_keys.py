"""Add foreign key constraints

Revision ID: 005
Revises: 004
Create Date: 2025-07-17 11:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add foreign key constraints for tenant relationships
    try:
        op.create_foreign_key(
            'fk_invoices_tenant_id', 
            'invoices', 
            'tenants', 
            ['tenant_id'], 
            ['id']
        )
    except Exception:
        pass  # Constraint might already exist
    
    try:
        op.create_foreign_key(
            'fk_accounting_entries_tenant_id', 
            'accounting_entries', 
            'tenants', 
            ['tenant_id'], 
            ['id']
        )
    except Exception:
        pass
    
    try:
        op.create_foreign_key(
            'fk_action_items_tenant_id', 
            'action_items', 
            'tenants', 
            ['tenant_id'], 
            ['id']
        )
    except Exception:
        pass
    
    try:
        op.create_foreign_key(
            'fk_invoice_vectors_tenant_id', 
            'invoice_vectors', 
            'tenants', 
            ['tenant_id'], 
            ['id']
        )
    except Exception:
        pass


def downgrade() -> None:
    # Remove foreign key constraints
    try:
        op.drop_constraint('fk_invoice_vectors_tenant_id', 'invoice_vectors', type_='foreignkey')
    except Exception:
        pass
    
    try:
        op.drop_constraint('fk_action_items_tenant_id', 'action_items', type_='foreignkey')
    except Exception:
        pass
    
    try:
        op.drop_constraint('fk_accounting_entries_tenant_id', 'accounting_entries', type_='foreignkey')
    except Exception:
        pass
    
    try:
        op.drop_constraint('fk_invoices_tenant_id', 'invoices', type_='foreignkey')
    except Exception:
        pass
